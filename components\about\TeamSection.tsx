'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'
import { 
  Linkedin, 
  Mail, 
  Phone,
  Award,
  BookOpen,
  Users,
  MapPin
} from 'lucide-react'

const TEAM_MEMBERS = [
  {
    name: '<PERSON><PERSON><PERSON><PERSON> <PERSON>',
    role: 'Founder & CEO',
    bio: 'Visionary leader with 15+ years in educational tourism. Passionate about transforming learning through travel experiences.',
    image: '/images/team/udbhav-patel.jpg',
    expertise: ['Educational Leadership', 'Adventure Tourism', 'Student Development'],
    achievements: [
      'Gujarat Tourism Excellence Award',
      '50,000+ students impacted',
      'Pioneer in educational travel'
    ],
    contact: {
      email: '<EMAIL>',
      phone: '+91 78780 05500',
      linkedin: 'https://linkedin.com/in/udbhav-patel'
    }
  },
  {
    name: '<PERSON><PERSON>',
    role: 'Head of Operations',
    bio: 'Operations expert ensuring seamless execution of every trip with meticulous attention to safety and quality.',
    image: '/images/team/priya-sharma.jpg',
    expertise: ['Operations Management', 'Safety Protocols', 'Quality Assurance'],
    achievements: [
      'Zero safety incidents record',
      'ISO 9001 certification lead',
      '500+ successful trips'
    ],
    contact: {
      email: '<EMAIL>',
      phone: '+91 78780 05501'
    }
  },
  {
    name: '<PERSON><PERSON>',
    role: 'Educational Program Director',
    bio: 'Former educator with deep understanding of curriculum alignment and experiential learning methodologies.',
    image: '/images/team/rajesh-kumar.jpg',
    expertise: ['Curriculum Design', 'Teacher Training', 'Learning Assessment'],
    achievements: [
      'M.Ed in Educational Psychology',
      '20+ years teaching experience',
      'Curriculum alignment specialist'
    ],
    contact: {
      email: '<EMAIL>',
      phone: '+91 78780 05502'
    }
  },
  {
    name: 'Anita Desai',
    role: 'Student Welfare Manager',
    bio: 'Dedicated to student safety and well-being, ensuring every child has a positive and secure experience.',
    image: '/images/team/anita-desai.jpg',
    expertise: ['Child Psychology', 'Crisis Management', 'Parent Communication'],
    achievements: [
      'Certified Child Safety Officer',
      'First Aid & CPR certified',
      '98% parent satisfaction rate'
    ],
    contact: {
      email: '<EMAIL>',
      phone: '+91 78780 05503'
    }
  },
  {
    name: 'Vikram Singh',
    role: 'Adventure Specialist',
    bio: 'Mountaineering expert and adventure guide with extensive experience in outdoor education and risk management.',
    image: '/images/team/vikram-singh.jpg',
    expertise: ['Mountaineering', 'Risk Assessment', 'Outdoor Education'],
    achievements: [
      'Everest Base Camp expedition',
      'Certified Mountain Guide',
      '1000+ adventure activities led'
    ],
    contact: {
      email: '<EMAIL>',
      phone: '+91 78780 05504'
    }
  },
  {
    name: 'Meera Patel',
    role: 'Cultural Programs Coordinator',
    bio: 'Cultural anthropologist specializing in immersive cultural experiences and community-based tourism.',
    image: '/images/team/meera-patel.jpg',
    expertise: ['Cultural Anthropology', 'Community Relations', 'Heritage Tourism'],
    achievements: [
      'PhD in Cultural Studies',
      'UNESCO heritage consultant',
      '50+ cultural partnerships'
    ],
    contact: {
      email: '<EMAIL>',
      phone: '+91 78780 05505'
    }
  }
]

export function TeamSection() {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  }

  return (
    <section className="py-20">
      <div className="max-w-7xl mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">Meet Our Team</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Our passionate team of educators, adventure specialists, and travel experts work together 
            to create unforgettable learning experiences for every student
          </p>
        </div>
        
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {TEAM_MEMBERS.map((member, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="group"
            >
              <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 h-full">
                {/* Profile Image */}
                <div className="relative w-32 h-32 mx-auto mb-6">
                  <div className="w-full h-full bg-gradient-to-r from-blue-100 to-green-100 rounded-full flex items-center justify-center">
                    <Users className="w-16 h-16 text-gray-400" />
                  </div>
                  {/* In a real implementation, you would use the actual image */}
                  {/* <Image
                    src={member.image}
                    alt={member.name}
                    fill
                    className="object-cover rounded-full"
                  /> */}
                </div>
                
                {/* Member Info */}
                <div className="text-center mb-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-2">{member.name}</h3>
                  <p className="text-blue-600 font-medium mb-3">{member.role}</p>
                  <p className="text-gray-600 text-sm leading-relaxed">{member.bio}</p>
                </div>
                
                {/* Expertise */}
                <div className="mb-6">
                  <h4 className="text-sm font-semibold text-gray-900 mb-3">Expertise:</h4>
                  <div className="flex flex-wrap gap-2">
                    {member.expertise.map((skill, skillIndex) => (
                      <span
                        key={skillIndex}
                        className="px-3 py-1 bg-blue-100 text-blue-700 text-xs rounded-full"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                </div>
                
                {/* Achievements */}
                <div className="mb-6">
                  <h4 className="text-sm font-semibold text-gray-900 mb-3 flex items-center gap-2">
                    <Award className="w-4 h-4" />
                    Key Achievements:
                  </h4>
                  <ul className="space-y-1">
                    {member.achievements.map((achievement, achievementIndex) => (
                      <li key={achievementIndex} className="text-xs text-gray-600 flex items-start gap-2">
                        <div className="w-1 h-1 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                        <span>{achievement}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                
                {/* Contact */}
                <div className="pt-4 border-t border-gray-200">
                  <div className="flex justify-center gap-4">
                    <a
                      href={`mailto:${member.contact.email}`}
                      className="p-2 bg-gray-100 rounded-full hover:bg-blue-100 hover:text-blue-600 transition-colors"
                      title="Email"
                    >
                      <Mail className="w-4 h-4" />
                    </a>
                    <a
                      href={`tel:${member.contact.phone}`}
                      className="p-2 bg-gray-100 rounded-full hover:bg-green-100 hover:text-green-600 transition-colors"
                      title="Phone"
                    >
                      <Phone className="w-4 h-4" />
                    </a>
                    {member.contact.linkedin && (
                      <a
                        href={member.contact.linkedin}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-2 bg-gray-100 rounded-full hover:bg-blue-100 hover:text-blue-600 transition-colors"
                        title="LinkedIn"
                      >
                        <Linkedin className="w-4 h-4" />
                      </a>
                    )}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Team Culture */}
        <div className="mt-16 bg-gradient-to-r from-blue-50 to-green-50 rounded-2xl p-8">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Our Team Culture</h3>
            <p className="text-gray-700 max-w-3xl mx-auto">
              We believe that a passionate, diverse, and dedicated team is the foundation of exceptional educational experiences.
            </p>
          </div>
          
          <div className="grid md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-3">
                <BookOpen className="w-6 h-6 text-white" />
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Continuous Learning</h4>
              <p className="text-sm text-gray-600">We invest in our team's growth and development</p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-3">
                <Users className="w-6 h-6 text-white" />
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Collaboration</h4>
              <p className="text-sm text-gray-600">We work together to achieve common goals</p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-3">
                <Award className="w-6 h-6 text-white" />
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Excellence</h4>
              <p className="text-sm text-gray-600">We strive for the highest standards in everything</p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-orange-600 rounded-full flex items-center justify-center mx-auto mb-3">
                <MapPin className="w-6 h-6 text-white" />
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Adventure Spirit</h4>
              <p className="text-sm text-gray-600">We embrace challenges and new experiences</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
