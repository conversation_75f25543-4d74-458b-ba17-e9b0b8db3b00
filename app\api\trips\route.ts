import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase';
import { verifySession } from '@/lib/supabase';
import type { TripFilters, CreateTripData } from '@/types/database';

// GET /api/trips - Get all trips with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const supabase = createServerSupabase();

    // Parse query parameters
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const destination = searchParams.get('destination');
    const difficulty = searchParams.get('difficulty');
    const minPrice = searchParams.get('minPrice');
    const maxPrice = searchParams.get('maxPrice');
    const minDuration = searchParams.get('minDuration');
    const maxDuration = searchParams.get('maxDuration');
    const dateFrom = searchParams.get('dateFrom');
    const dateTo = searchParams.get('dateTo');
    const search = searchParams.get('search');
    const featured = searchParams.get('featured') === 'true';
    const includeInactive = searchParams.get('includeInactive') === 'true';

    // Build query
    let query = supabase
      .from('trips')
      .select(`
        *,
        trip_images(*)
      `, { count: 'exact' });

    // Apply filters
    if (!includeInactive) {
      query = query.eq('is_active', true);
    }

    if (featured) {
      query = query.eq('is_featured', true);
    }

    if (destination) {
      query = query.ilike('destination', `%${destination}%`);
    }

    if (difficulty) {
      query = query.eq('difficulty', difficulty);
    }

    if (minPrice) {
      query = query.gte('price_per_person', parseFloat(minPrice));
    }

    if (maxPrice) {
      query = query.lte('price_per_person', parseFloat(maxPrice));
    }

    if (minDuration) {
      query = query.gte('duration_days', parseInt(minDuration));
    }

    if (maxDuration) {
      query = query.lte('duration_days', parseInt(maxDuration));
    }

    if (dateFrom) {
      query = query.gte('available_from', dateFrom);
    }

    if (dateTo) {
      query = query.lte('available_to', dateTo);
    }

    if (search) {
      query = query.or(`title.ilike.%${search}%,description.ilike.%${search}%,destination.ilike.%${search}%`);
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to);

    // Order by featured first, then by created_at
    query = query.order('is_featured', { ascending: false })
                 .order('created_at', { ascending: false });

    const { data: trips, error, count } = await query;

    if (error) {
      console.error('Error fetching trips:', error);
      return NextResponse.json(
        { error: 'Failed to fetch trips' },
        { status: 500 }
      );
    }

    const totalPages = Math.ceil((count || 0) / limit);

    return NextResponse.json({
      data: trips,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages,
      },
    });
  } catch (error) {
    console.error('Error in GET /api/trips:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/trips - Create a new trip (Admin only)
export async function POST(request: NextRequest) {
  try {
    const session = await verifySession('admin');
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body: CreateTripData = await request.json();
    const supabase = createServerSupabase();

    // Validate required fields
    const requiredFields = ['title', 'slug', 'destination', 'duration_days', 'max_participants', 'price_per_person', 'difficulty'];
    for (const field of requiredFields) {
      if (!body[field as keyof CreateTripData]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Check if slug is unique
    const { data: existingTrip } = await supabase
      .from('trips')
      .select('id')
      .eq('slug', body.slug)
      .single();

    if (existingTrip) {
      return NextResponse.json(
        { error: 'Trip with this slug already exists' },
        { status: 400 }
      );
    }

    // Create trip
    const { data: trip, error } = await supabase
      .from('trips')
      .insert({
        ...body,
        min_participants: body.min_participants || 1,
        is_active: body.is_active ?? true,
        is_featured: body.is_featured ?? false,
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating trip:', error);
      return NextResponse.json(
        { error: 'Failed to create trip' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      data: trip,
      message: 'Trip created successfully',
    }, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/trips:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
