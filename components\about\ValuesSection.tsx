'use client'

import { motion } from 'framer-motion'
import { 
  Heart, 
  Shield, 
  Lightbulb, 
  Users, 
  Leaf, 
  Star,
  Globe,
  BookOpen
} from 'lucide-react'

const VALUES = [
  {
    icon: Heart,
    title: 'Passion for Learning',
    description: 'We believe that learning should be exciting, engaging, and transformative. Every experience we create is driven by our passion for education.',
    color: 'from-red-500 to-pink-500'
  },
  {
    icon: Shield,
    title: 'Safety & Trust',
    description: 'The safety and well-being of our students is our top priority. We maintain the highest safety standards and build lasting trust with families.',
    color: 'from-blue-500 to-indigo-500'
  },
  {
    icon: Lightbulb,
    title: 'Innovation',
    description: 'We continuously innovate our programs to incorporate new learning methodologies and technologies that enhance the educational experience.',
    color: 'from-yellow-500 to-orange-500'
  },
  {
    icon: Users,
    title: 'Collaboration',
    description: 'We work closely with educators, parents, and students to create customized experiences that meet specific learning objectives.',
    color: 'from-green-500 to-emerald-500'
  },
  {
    icon: Leaf,
    title: 'Sustainability',
    description: 'We are committed to responsible tourism that respects local communities and preserves the environment for future generations.',
    color: 'from-green-600 to-teal-600'
  },
  {
    icon: Star,
    title: 'Excellence',
    description: 'We strive for excellence in every aspect of our service, from planning to execution, ensuring memorable and meaningful experiences.',
    color: 'from-purple-500 to-violet-500'
  },
  {
    icon: Globe,
    title: 'Cultural Respect',
    description: 'We promote cultural understanding and respect, helping students become global citizens who appreciate diversity.',
    color: 'from-cyan-500 to-blue-500'
  },
  {
    icon: BookOpen,
    title: 'Lifelong Learning',
    description: 'We inspire a love for lifelong learning, encouraging students to remain curious and open to new experiences throughout their lives.',
    color: 'from-indigo-500 to-purple-500'
  }
]

export function ValuesSection() {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  }

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">Our Core Values</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            These fundamental principles guide everything we do and shape the experiences we create for our students
          </p>
        </div>
        
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid md:grid-cols-2 lg:grid-cols-4 gap-8"
        >
          {VALUES.map((value, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="group"
            >
              <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300 h-full">
                <div className={`w-16 h-16 bg-gradient-to-r ${value.color} rounded-full flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  <value.icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">{value.title}</h3>
                <p className="text-gray-600 leading-relaxed">{value.description}</p>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  )
}
