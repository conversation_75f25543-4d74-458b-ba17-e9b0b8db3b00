'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  ChevronLeft,
  ChevronRight,
  Check,
  User,
  Calendar,
  CreditCard,
  MapPin,
  Users,
  Phone,
  Mail,
  FileText
} from 'lucide-react'
import Button from '@/components/ui/Button'

interface BookingData {
  // Step 1: Trip Selection
  tripId: string
  selectedDate: string
  participants: number

  // Step 2: Traveler Details
  leadTraveler: {
    firstName: string
    lastName: string
    email: string
    phone: string
    age: number
    emergencyContact: string
  }
  additionalTravelers: Array<{
    firstName: string
    lastName: string
    age: number
    relation: string
  }>

  // Step 3: Customizations
  roomPreference: 'shared' | 'private'
  dietaryRequirements: string[]
  specialRequests: string

  // Step 4: Payment
  paymentMethod: 'full' | 'partial'
  paymentGateway: 'razorpay' | 'stripe'
}

interface BookingWizardProps {
  tripId: string
  tripTitle: string
  tripPrice: number
  onComplete: (data: BookingData) => void
  onCancel: () => void
}

const STEPS = [
  { id: 1, title: 'Trip Details', icon: Calendar },
  { id: 2, title: 'Traveler Info', icon: User },
  { id: 3, title: 'Preferences', icon: MapPin },
  { id: 4, title: 'Payment', icon: CreditCard }
]

export function BookingWizard({
  tripId,
  tripTitle,
  tripPrice,
  onComplete,
  onCancel
}: BookingWizardProps) {
  const [currentStep, setCurrentStep] = useState(1)
  const [bookingData, setBookingData] = useState<BookingData>({
    tripId,
    selectedDate: '',
    participants: 1,
    leadTraveler: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      age: 18,
      emergencyContact: ''
    },
    additionalTravelers: [],
    roomPreference: 'shared',
    dietaryRequirements: [],
    specialRequests: '',
    paymentMethod: 'partial',
    paymentGateway: 'razorpay'
  })

  const updateBookingData = (updates: Partial<BookingData>) => {
    setBookingData(prev => ({ ...prev, ...updates }))
  }

  const nextStep = () => {
    if (currentStep < STEPS.length) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const isStepValid = (step: number): boolean => {
    switch (step) {
      case 1:
        return bookingData.selectedDate !== '' && bookingData.participants > 0
      case 2:
        return bookingData.leadTraveler.firstName !== '' &&
               bookingData.leadTraveler.lastName !== '' &&
               bookingData.leadTraveler.email !== '' &&
               bookingData.leadTraveler.phone !== ''
      case 3:
        return true // Preferences are optional
      case 4:
        return true // Payment validation happens on submit
      default:
        return false
    }
  }

  const totalPrice = tripPrice * bookingData.participants

  return (
    <div className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4">
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
      >
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-2xl font-bold text-gray-900">Book Your Trip</h2>
            <button
              onClick={onCancel}
              className="text-gray-400 hover:text-gray-600"
            >
              ×
            </button>
          </div>
          <p className="text-gray-600">{tripTitle}</p>

          {/* Progress Steps */}
          <div className="flex items-center justify-between mt-6">
            {STEPS.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className={`
                  w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium
                  ${currentStep >= step.id
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 text-gray-600'
                  }
                `}>
                  {currentStep > step.id ? (
                    <Check className="w-5 h-5" />
                  ) : (
                    <step.icon className="w-5 h-5" />
                  )}
                </div>
                <div className="ml-3 hidden sm:block">
                  <div className={`text-sm font-medium ${
                    currentStep >= step.id ? 'text-blue-600' : 'text-gray-500'
                  }`}>
                    {step.title}
                  </div>
                </div>
                {index < STEPS.length - 1 && (
                  <div className={`w-12 h-0.5 mx-4 ${
                    currentStep > step.id ? 'bg-blue-600' : 'bg-gray-200'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Step Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              {currentStep === 1 && (
                <TripDetailsStep
                  data={bookingData}
                  onChange={updateBookingData}
                  tripPrice={tripPrice}
                />
              )}
              {currentStep === 2 && (
                <TravelerDetailsStep
                  data={bookingData}
                  onChange={updateBookingData}
                />
              )}
              {currentStep === 3 && (
                <PreferencesStep
                  data={bookingData}
                  onChange={updateBookingData}
                />
              )}
              {currentStep === 4 && (
                <PaymentStep
                  data={bookingData}
                  onChange={updateBookingData}
                  totalPrice={totalPrice}
                />
              )}
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 flex items-center justify-between">
          <div className="text-sm text-gray-600">
            Step {currentStep} of {STEPS.length}
          </div>

          <div className="flex gap-3">
            {currentStep > 1 && (
              <Button
                variant="outline"
                onClick={prevStep}
                className="flex items-center gap-2"
              >
                <ChevronLeft className="w-4 h-4" />
                Previous
              </Button>
            )}

            {currentStep < STEPS.length ? (
              <Button
                onClick={nextStep}
                disabled={!isStepValid(currentStep)}
                className="flex items-center gap-2 bg-gradient-to-r from-blue-600 to-green-600"
              >
                Next
                <ChevronRight className="w-4 h-4" />
              </Button>
            ) : (
              <Button
                onClick={() => onComplete(bookingData)}
                disabled={!isStepValid(currentStep)}
                className="bg-gradient-to-r from-blue-600 to-green-600"
              >
                Complete Booking
              </Button>
            )}
          </div>
        </div>
      </motion.div>
    </div>
  )
}

// Step 1: Trip Details
function TripDetailsStep({
  data,
  onChange,
  tripPrice
}: {
  data: BookingData
  onChange: (updates: Partial<BookingData>) => void
  tripPrice: number
}) {
  const availableDates = [
    { start: '2024-02-15', end: '2024-02-23', available: true },
    { start: '2024-02-18', end: '2024-02-26', available: true },
    { start: '2024-03-11', end: '2024-03-19', available: true },
    { start: '2024-04-07', end: '2024-04-15', available: false },
    { start: '2024-05-02', end: '2024-05-10', available: true }
  ]

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    })
  }

  return (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold text-gray-900">Select Your Trip Details</h3>

      {/* Date Selection */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          <Calendar className="w-4 h-4 inline mr-2" />
          Departure Date
        </label>
        <div className="grid gap-3">
          {availableDates.map((date, index) => (
            <label
              key={index}
              className={`
                p-4 border rounded-lg cursor-pointer transition-colors
                ${data.selectedDate === date.start
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
                }
                ${!date.available ? 'opacity-50 cursor-not-allowed' : ''}
              `}
            >
              <input
                type="radio"
                name="selectedDate"
                value={date.start}
                checked={data.selectedDate === date.start}
                onChange={(e) => onChange({ selectedDate: e.target.value })}
                disabled={!date.available}
                className="sr-only"
              />
              <div className="flex justify-between items-center">
                <div>
                  <div className="font-medium">
                    {formatDate(date.start)} - {formatDate(date.end)}
                  </div>
                  <div className="text-sm text-gray-600">9 Days, 8 Nights</div>
                </div>
                <div className="text-right">
                  {date.available ? (
                    <div className="text-green-600 font-medium">Available</div>
                  ) : (
                    <div className="text-red-600 font-medium">Sold Out</div>
                  )}
                </div>
              </div>
            </label>
          ))}
        </div>
      </div>

      {/* Participants */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          <Users className="w-4 h-4 inline mr-2" />
          Number of Participants
        </label>
        <div className="flex items-center gap-4">
          <button
            type="button"
            onClick={() => onChange({ participants: Math.max(1, data.participants - 1) })}
            className="w-10 h-10 rounded-lg border border-gray-300 flex items-center justify-center hover:bg-gray-50"
          >
            -
          </button>
          <span className="text-xl font-medium w-12 text-center">{data.participants}</span>
          <button
            type="button"
            onClick={() => onChange({ participants: data.participants + 1 })}
            className="w-10 h-10 rounded-lg border border-gray-300 flex items-center justify-center hover:bg-gray-50"
          >
            +
          </button>
        </div>
      </div>

      {/* Price Summary */}
      <div className="p-4 bg-blue-50 rounded-lg">
        <div className="flex justify-between items-center mb-2">
          <span>Price per person:</span>
          <span>₹{tripPrice.toLocaleString()}</span>
        </div>
        <div className="flex justify-between items-center mb-2">
          <span>Participants:</span>
          <span>{data.participants}</span>
        </div>
        <div className="border-t border-blue-200 pt-2 flex justify-between items-center font-bold">
          <span>Total Amount:</span>
          <span className="text-xl text-blue-600">₹{(tripPrice * data.participants).toLocaleString()}</span>
        </div>
      </div>
    </div>
  )
}

// Step 2: Traveler Details
function TravelerDetailsStep({
  data,
  onChange
}: {
  data: BookingData
  onChange: (updates: Partial<BookingData>) => void
}) {
  const updateLeadTraveler = (updates: Partial<BookingData['leadTraveler']>) => {
    onChange({
      leadTraveler: { ...data.leadTraveler, ...updates }
    })
  }

  return (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold text-gray-900">Traveler Information</h3>

      {/* Lead Traveler */}
      <div className="p-6 border border-gray-200 rounded-lg">
        <h4 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
          <User className="w-5 h-5" />
          Lead Traveler (Primary Contact)
        </h4>

        <div className="grid md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              First Name *
            </label>
            <input
              type="text"
              value={data.leadTraveler.firstName}
              onChange={(e) => updateLeadTraveler({ firstName: e.target.value })}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              placeholder="Enter first name"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Last Name *
            </label>
            <input
              type="text"
              value={data.leadTraveler.lastName}
              onChange={(e) => updateLeadTraveler({ lastName: e.target.value })}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              placeholder="Enter last name"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Mail className="w-4 h-4 inline mr-1" />
              Email Address *
            </label>
            <input
              type="email"
              value={data.leadTraveler.email}
              onChange={(e) => updateLeadTraveler({ email: e.target.value })}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              placeholder="Enter email address"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Phone className="w-4 h-4 inline mr-1" />
              Phone Number *
            </label>
            <input
              type="tel"
              value={data.leadTraveler.phone}
              onChange={(e) => updateLeadTraveler({ phone: e.target.value })}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              placeholder="Enter phone number"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Age
            </label>
            <input
              type="number"
              value={data.leadTraveler.age}
              onChange={(e) => updateLeadTraveler({ age: parseInt(e.target.value) || 18 })}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              min="12"
              max="80"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Emergency Contact
            </label>
            <input
              type="text"
              value={data.leadTraveler.emergencyContact}
              onChange={(e) => updateLeadTraveler({ emergencyContact: e.target.value })}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              placeholder="Emergency contact number"
            />
          </div>
        </div>
      </div>

      {/* Additional Travelers */}
      {data.participants > 1 && (
        <div className="p-6 border border-gray-200 rounded-lg">
          <h4 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
            <Users className="w-5 h-5" />
            Additional Travelers ({data.participants - 1})
          </h4>
          <p className="text-sm text-gray-600 mb-4">
            Please provide details for the remaining {data.participants - 1} participant(s).
          </p>

          {/* Note: In a real implementation, you'd have dynamic forms for each additional traveler */}
          <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-sm text-yellow-800">
              Additional traveler details can be provided after initial booking confirmation.
              Our team will contact you to collect the remaining information.
            </p>
          </div>
        </div>
      )}
    </div>
  )
}

// Step 3: Preferences
function PreferencesStep({
  data,
  onChange
}: {
  data: BookingData
  onChange: (updates: Partial<BookingData>) => void
}) {
  const dietaryOptions = [
    'Vegetarian', 'Vegan', 'Jain', 'No Onion/Garlic', 'Gluten-Free', 'Diabetic'
  ]

  const toggleDietaryRequirement = (requirement: string) => {
    const current = data.dietaryRequirements || []
    const updated = current.includes(requirement)
      ? current.filter(r => r !== requirement)
      : [...current, requirement]
    onChange({ dietaryRequirements: updated })
  }

  return (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold text-gray-900">Trip Preferences</h3>

      {/* Room Preference */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          <MapPin className="w-4 h-4 inline mr-2" />
          Accommodation Preference
        </label>
        <div className="grid md:grid-cols-2 gap-4">
          <label className={`
            p-4 border rounded-lg cursor-pointer transition-colors
            ${data.roomPreference === 'shared'
              ? 'border-blue-500 bg-blue-50'
              : 'border-gray-200 hover:border-gray-300'
            }
          `}>
            <input
              type="radio"
              name="roomPreference"
              value="shared"
              checked={data.roomPreference === 'shared'}
              onChange={(e) => onChange({ roomPreference: e.target.value as 'shared' | 'private' })}
              className="sr-only"
            />
            <div>
              <div className="font-medium">Shared Accommodation</div>
              <div className="text-sm text-gray-600">Quad sharing basis (4 per room)</div>
              <div className="text-sm text-green-600 font-medium">Included in package</div>
            </div>
          </label>

          <label className={`
            p-4 border rounded-lg cursor-pointer transition-colors
            ${data.roomPreference === 'private'
              ? 'border-blue-500 bg-blue-50'
              : 'border-gray-200 hover:border-gray-300'
            }
          `}>
            <input
              type="radio"
              name="roomPreference"
              value="private"
              checked={data.roomPreference === 'private'}
              onChange={(e) => onChange({ roomPreference: e.target.value as 'shared' | 'private' })}
              className="sr-only"
            />
            <div>
              <div className="font-medium">Private Room</div>
              <div className="text-sm text-gray-600">Single/Double occupancy</div>
              <div className="text-sm text-orange-600 font-medium">Additional charges apply</div>
            </div>
          </label>
        </div>
      </div>

      {/* Dietary Requirements */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Dietary Requirements
        </label>
        <div className="grid md:grid-cols-3 gap-3">
          {dietaryOptions.map((option) => (
            <label
              key={option}
              className={`
                p-3 border rounded-lg cursor-pointer transition-colors text-center
                ${(data.dietaryRequirements || []).includes(option)
                  ? 'border-green-500 bg-green-50 text-green-700'
                  : 'border-gray-200 hover:border-gray-300'
                }
              `}
            >
              <input
                type="checkbox"
                checked={(data.dietaryRequirements || []).includes(option)}
                onChange={() => toggleDietaryRequirement(option)}
                className="sr-only"
              />
              <span className="text-sm font-medium">{option}</span>
            </label>
          ))}
        </div>
      </div>

      {/* Special Requests */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          <FileText className="w-4 h-4 inline mr-2" />
          Special Requests or Requirements
        </label>
        <textarea
          value={data.specialRequests}
          onChange={(e) => onChange({ specialRequests: e.target.value })}
          rows={4}
          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
          placeholder="Any special requirements, medical conditions, or requests you'd like us to know about..."
        />
      </div>
    </div>
  )
}

// Step 4: Payment
function PaymentStep({
  data,
  onChange,
  totalPrice
}: {
  data: BookingData
  onChange: (updates: Partial<BookingData>) => void
  totalPrice: number
}) {
  const partialAmount = Math.round(totalPrice * 0.5) // 50% advance
  const remainingAmount = totalPrice - partialAmount

  return (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold text-gray-900">Payment Details</h3>

      {/* Payment Method */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          <CreditCard className="w-4 h-4 inline mr-2" />
          Payment Option
        </label>
        <div className="space-y-3">
          <label className={`
            p-4 border rounded-lg cursor-pointer transition-colors
            ${data.paymentMethod === 'partial'
              ? 'border-blue-500 bg-blue-50'
              : 'border-gray-200 hover:border-gray-300'
            }
          `}>
            <input
              type="radio"
              name="paymentMethod"
              value="partial"
              checked={data.paymentMethod === 'partial'}
              onChange={(e) => onChange({ paymentMethod: e.target.value as 'full' | 'partial' })}
              className="sr-only"
            />
            <div className="flex justify-between items-center">
              <div>
                <div className="font-medium">Pay 50% Now</div>
                <div className="text-sm text-gray-600">Remaining 50% before trip departure</div>
              </div>
              <div className="text-right">
                <div className="text-xl font-bold text-blue-600">₹{partialAmount.toLocaleString()}</div>
                <div className="text-sm text-gray-600">Now</div>
              </div>
            </div>
          </label>

          <label className={`
            p-4 border rounded-lg cursor-pointer transition-colors
            ${data.paymentMethod === 'full'
              ? 'border-blue-500 bg-blue-50'
              : 'border-gray-200 hover:border-gray-300'
            }
          `}>
            <input
              type="radio"
              name="paymentMethod"
              value="full"
              checked={data.paymentMethod === 'full'}
              onChange={(e) => onChange({ paymentMethod: e.target.value as 'full' | 'partial' })}
              className="sr-only"
            />
            <div className="flex justify-between items-center">
              <div>
                <div className="font-medium">Pay Full Amount</div>
                <div className="text-sm text-gray-600">Complete payment now</div>
              </div>
              <div className="text-right">
                <div className="text-xl font-bold text-green-600">₹{totalPrice.toLocaleString()}</div>
                <div className="text-sm text-green-600">5% discount applied</div>
              </div>
            </div>
          </label>
        </div>
      </div>

      {/* Payment Gateway */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Payment Gateway
        </label>
        <div className="grid md:grid-cols-2 gap-4">
          <label className={`
            p-4 border rounded-lg cursor-pointer transition-colors
            ${data.paymentGateway === 'razorpay'
              ? 'border-blue-500 bg-blue-50'
              : 'border-gray-200 hover:border-gray-300'
            }
          `}>
            <input
              type="radio"
              name="paymentGateway"
              value="razorpay"
              checked={data.paymentGateway === 'razorpay'}
              onChange={(e) => onChange({ paymentGateway: e.target.value as 'razorpay' | 'stripe' })}
              className="sr-only"
            />
            <div className="text-center">
              <div className="font-medium">Razorpay</div>
              <div className="text-sm text-gray-600">UPI, Cards, Net Banking</div>
            </div>
          </label>

          <label className={`
            p-4 border rounded-lg cursor-pointer transition-colors
            ${data.paymentGateway === 'stripe'
              ? 'border-blue-500 bg-blue-50'
              : 'border-gray-200 hover:border-gray-300'
            }
          `}>
            <input
              type="radio"
              name="paymentGateway"
              value="stripe"
              checked={data.paymentGateway === 'stripe'}
              onChange={(e) => onChange({ paymentGateway: e.target.value as 'razorpay' | 'stripe' })}
              className="sr-only"
            />
            <div className="text-center">
              <div className="font-medium">Stripe</div>
              <div className="text-sm text-gray-600">International Cards</div>
            </div>
          </label>
        </div>
      </div>

      {/* Payment Summary */}
      <div className="p-6 bg-gray-50 rounded-lg">
        <h4 className="font-semibold text-gray-900 mb-4">Payment Summary</h4>
        <div className="space-y-2">
          <div className="flex justify-between">
            <span>Trip Cost ({data.participants} participants):</span>
            <span>₹{totalPrice.toLocaleString()}</span>
          </div>
          {data.paymentMethod === 'full' && (
            <div className="flex justify-between text-green-600">
              <span>Full Payment Discount (5%):</span>
              <span>-₹{Math.round(totalPrice * 0.05).toLocaleString()}</span>
            </div>
          )}
          <div className="border-t border-gray-300 pt-2 flex justify-between font-bold text-lg">
            <span>Amount to Pay Now:</span>
            <span className="text-blue-600">
              ₹{data.paymentMethod === 'full'
                ? Math.round(totalPrice * 0.95).toLocaleString()
                : partialAmount.toLocaleString()
              }
            </span>
          </div>
          {data.paymentMethod === 'partial' && (
            <div className="text-sm text-gray-600">
              Remaining ₹{remainingAmount.toLocaleString()} due 15 days before departure
            </div>
          )}
        </div>
      </div>
    </div>
  )
}