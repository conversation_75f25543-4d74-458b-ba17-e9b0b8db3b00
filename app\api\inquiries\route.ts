import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase';
import { verifySession } from '@/lib/supabase';
import type { CreateInquiryData } from '@/types/database';

// GET /api/inquiries - Get inquiries (Admin only)
export async function GET(request: NextRequest) {
  try {
    const session = await verifySession('admin');
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const supabase = createServerSupabase();

    // Parse query parameters
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const inquiryType = searchParams.get('inquiryType');
    const tripId = searchParams.get('tripId');
    const search = searchParams.get('search');

    // Build query
    let query = supabase
      .from('inquiries')
      .select(`
        *,
        trip:trips(id, title, destination)
      `, { count: 'exact' });

    // Apply filters
    if (status) {
      query = query.eq('status', status);
    }

    if (inquiryType) {
      query = query.eq('inquiry_type', inquiryType);
    }

    if (tripId) {
      query = query.eq('trip_id', tripId);
    }

    if (search) {
      query = query.or(`name.ilike.%${search}%,email.ilike.%${search}%,subject.ilike.%${search}%,message.ilike.%${search}%`);
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to);

    // Order by status (new first) then by created_at
    query = query.order('status', { ascending: true })
                 .order('created_at', { ascending: false });

    const { data: inquiries, error, count } = await query;

    if (error) {
      console.error('Error fetching inquiries:', error);
      return NextResponse.json(
        { error: 'Failed to fetch inquiries' },
        { status: 500 }
      );
    }

    const totalPages = Math.ceil((count || 0) / limit);

    return NextResponse.json({
      data: inquiries,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages,
      },
    });
  } catch (error) {
    console.error('Error in GET /api/inquiries:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/inquiries - Create a new inquiry
export async function POST(request: NextRequest) {
  try {
    const body: CreateInquiryData = await request.json();
    const supabase = createServerSupabase();

    // Validate required fields
    const requiredFields = ['name', 'email', 'message'];
    for (const field of requiredFields) {
      if (!body[field as keyof CreateInquiryData]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(body.email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Validate trip exists if trip_id is provided
    if (body.trip_id) {
      const { data: trip, error: tripError } = await supabase
        .from('trips')
        .select('id, title')
        .eq('id', body.trip_id)
        .single();

      if (tripError || !trip) {
        return NextResponse.json(
          { error: 'Trip not found' },
          { status: 404 }
        );
      }
    }

    // Create inquiry
    const { data: inquiry, error } = await supabase
      .from('inquiries')
      .insert({
        ...body,
        status: 'new',
      })
      .select(`
        *,
        trip:trips(id, title, destination)
      `)
      .single();

    if (error) {
      console.error('Error creating inquiry:', error);
      return NextResponse.json(
        { error: 'Failed to create inquiry' },
        { status: 500 }
      );
    }

    // TODO: Send notification email to admin
    // TODO: Send confirmation email to user

    return NextResponse.json({
      data: inquiry,
      message: 'Inquiry submitted successfully. We will get back to you soon!',
    }, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/inquiries:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
