/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/not-found";
exports.ids = ["app/not-found"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n          '__DEFAULT__',\n          {},\n          {\n            defaultPage: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/parallel-route-default */ \"(rsc)/./node_modules/next/dist/client/components/parallel-route-default.js\", 23)), \"next/dist/client/components/parallel-route-default\"],\n          }\n        ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/not-found\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/not-found\",\n        pathname: \"/not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Ccontexts%5CAuthContext.tsx&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Poppins%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%2C%22800%22%5D%2C%22variable%22%3A%22--font-poppins%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22poppins%22%7D&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp%5Cglobals.css&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Ccontexts%5CAuthContext.tsx&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Poppins%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%2C%22800%22%5D%2C%22variable%22%3A%22--font-poppins%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22poppins%22%7D&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp%5Cglobals.css&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./contexts/AuthContext.tsx */ \"(ssr)/./contexts/AuthContext.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDcGVlYnMlNUNEb2N1bWVudHMlNUNwcm9qZWN0cyU1Q3A3LWNvbXByZWhlbnNpdmUlNUNjb250ZXh0cyU1Q0F1dGhDb250ZXh0LnRzeCZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q3BlZWJzJTVDRG9jdW1lbnRzJTVDcHJvamVjdHMlNUNwNy1jb21wcmVoZW5zaXZlJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2ZvbnQlNUNnb29nbGUlNUN0YXJnZXQuY3NzJTNGJTdCJTIycGF0aCUyMiUzQSUyMmFwcCU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpbXBvcnQlMjIlM0ElMjJJbnRlciUyMiUyQyUyMmFyZ3VtZW50cyUyMiUzQSU1QiU3QiUyMnN1YnNldHMlMjIlM0ElNUIlMjJsYXRpbiUyMiU1RCUyQyUyMnZhcmlhYmxlJTIyJTNBJTIyLS1mb250LWludGVyJTIyJTJDJTIyZGlzcGxheSUyMiUzQSUyMnN3YXAlMjIlN0QlNUQlMkMlMjJ2YXJpYWJsZU5hbWUlMjIlM0ElMjJpbnRlciUyMiU3RCZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q3BlZWJzJTVDRG9jdW1lbnRzJTVDcHJvamVjdHMlNUNwNy1jb21wcmVoZW5zaXZlJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2ZvbnQlNUNnb29nbGUlNUN0YXJnZXQuY3NzJTNGJTdCJTIycGF0aCUyMiUzQSUyMmFwcCU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpbXBvcnQlMjIlM0ElMjJQb3BwaW5zJTIyJTJDJTIyYXJndW1lbnRzJTIyJTNBJTVCJTdCJTIyc3Vic2V0cyUyMiUzQSU1QiUyMmxhdGluJTIyJTVEJTJDJTIyd2VpZ2h0JTIyJTNBJTVCJTIyMzAwJTIyJTJDJTIyNDAwJTIyJTJDJTIyNTAwJTIyJTJDJTIyNjAwJTIyJTJDJTIyNzAwJTIyJTJDJTIyODAwJTIyJTVEJTJDJTIydmFyaWFibGUlMjIlM0ElMjItLWZvbnQtcG9wcGlucyUyMiUyQyUyMmRpc3BsYXklMjIlM0ElMjJzd2FwJTIyJTdEJTVEJTJDJTIydmFyaWFibGVOYW1lJTIyJTNBJTIycG9wcGlucyUyMiU3RCZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q3BlZWJzJTVDRG9jdW1lbnRzJTVDcHJvamVjdHMlNUNwNy1jb21wcmVoZW5zaXZlJTVDYXBwJTVDZ2xvYmFscy5jc3Mmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcG9zaXRpdmU3LXRvdXJpc20td2Vic2l0ZS8/ZjU2YiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHBlZWJzXFxcXERvY3VtZW50c1xcXFxwcm9qZWN0c1xcXFxwNy1jb21wcmVoZW5zaXZlXFxcXGNvbnRleHRzXFxcXEF1dGhDb250ZXh0LnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Ccontexts%5CAuthContext.tsx&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Poppins%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%2C%22800%22%5D%2C%22variable%22%3A%22--font-poppins%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22poppins%22%7D&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp%5Cglobals.css&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./contexts/AuthContext.tsx":
/*!**********************************!*\
  !*** ./contexts/AuthContext.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createClientSupabase)();\n    // Fetch user profile data\n    const fetchUserProfile = async (userId)=>{\n        try {\n            const { data, error } = await supabase.from(\"users\").select(\"*\").eq(\"id\", userId).single();\n            if (error) {\n                console.error(\"Error fetching user profile:\", error);\n                return null;\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error fetching user profile:\", error);\n            return null;\n        }\n    };\n    // Initialize auth state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeAuth = async ()=>{\n            try {\n                const { data: { session }, error } = await supabase.auth.getSession();\n                if (error) {\n                    console.error(\"Error getting session:\", error);\n                    setLoading(false);\n                    return;\n                }\n                setSession(session);\n                if (session?.user) {\n                    const userProfile = await fetchUserProfile(session.user.id);\n                    setUser(userProfile);\n                }\n            } catch (error) {\n                console.error(\"Error initializing auth:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        initializeAuth();\n        // Listen for auth changes\n        const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session)=>{\n            console.log(\"Auth state changed:\", event, session?.user?.id);\n            setSession(session);\n            if (session?.user) {\n                const userProfile = await fetchUserProfile(session.user.id);\n                setUser(userProfile);\n            } else {\n                setUser(null);\n            }\n            setLoading(false);\n        });\n        return ()=>{\n            subscription.unsubscribe();\n        };\n    }, []);\n    // Sign up function\n    const signUp = async (email, password, userData)=>{\n        try {\n            const { data, error } = await supabase.auth.signUp({\n                email,\n                password,\n                options: {\n                    data: {\n                        full_name: userData.full_name,\n                        phone: userData.phone\n                    }\n                }\n            });\n            if (error) {\n                return {\n                    data: null,\n                    error: error.message\n                };\n            }\n            // Create user profile\n            if (data.user) {\n                const { error: profileError } = await supabase.from(\"users\").insert({\n                    id: data.user.id,\n                    email: data.user.email,\n                    full_name: userData.full_name,\n                    phone: userData.phone,\n                    role: \"customer\"\n                });\n                if (profileError) {\n                    console.error(\"Error creating user profile:\", profileError);\n                    return {\n                        data: null,\n                        error: \"Failed to create user profile\"\n                    };\n                }\n            }\n            return {\n                data,\n                error: null\n            };\n        } catch (error) {\n            return {\n                data: null,\n                error: error.message\n            };\n        }\n    };\n    // Sign in function\n    const signIn = async (email, password)=>{\n        try {\n            const { data, error } = await supabase.auth.signInWithPassword({\n                email,\n                password\n            });\n            if (error) {\n                return {\n                    data: null,\n                    error: error.message\n                };\n            }\n            return {\n                data,\n                error: null\n            };\n        } catch (error) {\n            return {\n                data: null,\n                error: error.message\n            };\n        }\n    };\n    // Sign out function\n    const signOut = async ()=>{\n        try {\n            const { error } = await supabase.auth.signOut();\n            if (error) {\n                return {\n                    error: error.message\n                };\n            }\n            return {\n                error: null\n            };\n        } catch (error) {\n            return {\n                error: error.message\n            };\n        }\n    };\n    // Reset password function\n    const resetPassword = async (email)=>{\n        try {\n            const { error } = await supabase.auth.resetPasswordForEmail(email, {\n                redirectTo: `${window.location.origin}/auth/reset-password`\n            });\n            if (error) {\n                return {\n                    error: error.message\n                };\n            }\n            return {\n                error: null\n            };\n        } catch (error) {\n            return {\n                error: error.message\n            };\n        }\n    };\n    // Update profile function\n    const updateProfile = async (updates)=>{\n        if (!user) {\n            return {\n                data: null,\n                error: \"No user logged in\"\n            };\n        }\n        try {\n            const { data, error } = await supabase.from(\"users\").update({\n                ...updates,\n                updated_at: new Date().toISOString()\n            }).eq(\"id\", user.id).select().single();\n            if (error) {\n                return {\n                    data: null,\n                    error: error.message\n                };\n            }\n            setUser(data);\n            return {\n                data,\n                error: null\n            };\n        } catch (error) {\n            return {\n                data: null,\n                error: error.message\n            };\n        }\n    };\n    // Refresh user data\n    const refreshUser = async ()=>{\n        if (!session?.user) return;\n        const userProfile = await fetchUserProfile(session.user.id);\n        setUser(userProfile);\n    };\n    const value = {\n        user,\n        session,\n        loading,\n        signUp,\n        signIn,\n        signOut,\n        resetPassword,\n        updateProfile,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 252,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClientSupabase: () => (/* binding */ createClientSupabase),\n/* harmony export */   deleteFile: () => (/* binding */ deleteFile),\n/* harmony export */   getSession: () => (/* binding */ getSession),\n/* harmony export */   subscribeToTable: () => (/* binding */ subscribeToTable),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   unsubscribe: () => (/* binding */ unsubscribe),\n/* harmony export */   uploadFile: () => (/* binding */ uploadFile),\n/* harmony export */   withErrorHandling: () => (/* binding */ withErrorHandling)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// Environment variables with fallbacks for development\nconst supabaseUrl = \"https://placeholder.supabase.co\" || 0;\nconst supabaseAnonKey = \"placeholder-anon-key\" || 0;\n// Default client for general use\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// Client for use in client components (alias for compatibility)\nconst createClientSupabase = ()=>supabase;\n// Helper function to get user session (client-side)\nconst getSession = async ()=>{\n    const { data: { session }, error } = await supabase.auth.getSession();\n    if (error) {\n        console.error(\"Error getting session:\", error);\n        return null;\n    }\n    return session;\n};\n// Storage helpers\nconst uploadFile = async (bucket, path, file, options)=>{\n    const { data, error } = await supabase.storage.from(bucket).upload(path, file, options);\n    if (error) {\n        console.error(\"Error uploading file:\", error);\n        return {\n            data: null,\n            error\n        };\n    }\n    // Get public URL\n    const { data: { publicUrl } } = supabase.storage.from(bucket).getPublicUrl(path);\n    return {\n        data: {\n            ...data,\n            publicUrl\n        },\n        error: null\n    };\n};\nconst deleteFile = async (bucket, path)=>{\n    const { error } = await supabase.storage.from(bucket).remove([\n        path\n    ]);\n    if (error) {\n        console.error(\"Error deleting file:\", error);\n        return {\n            error\n        };\n    }\n    return {\n        error: null\n    };\n};\n// Database helpers\nconst withErrorHandling = async (operation)=>{\n    try {\n        const { data, error } = await operation();\n        if (error) {\n            console.error(\"Database error:\", error);\n            return {\n                data: null,\n                error: error.message || \"Database operation failed\"\n            };\n        }\n        return {\n            data,\n            error: null\n        };\n    } catch (err) {\n        console.error(\"Unexpected error:\", err);\n        return {\n            data: null,\n            error: \"An unexpected error occurred\"\n        };\n    }\n};\n// Real-time subscription helpers\nconst subscribeToTable = (table, callback, filter)=>{\n    let subscription = supabase.channel(`${table}_changes`).on(\"postgres_changes\", {\n        event: \"*\",\n        schema: \"public\",\n        table,\n        filter\n    }, callback).subscribe();\n    return subscription;\n};\nconst unsubscribe = (subscription)=>{\n    if (subscription) {\n        subscription.unsubscribe();\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/supabase.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"4be39902af1f\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wb3NpdGl2ZTctdG91cmlzbS13ZWJzaXRlLy4vYXBwL2dsb2JhbHMuY3NzP2MyYjAiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI0YmUzOTkwMmFmMWZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_800_variable_font_poppins_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\",\"800\"],\"variable\":\"--font-poppins\",\"display\":\"swap\"}],\"variableName\":\"poppins\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Poppins\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\",\\\"800\\\"],\\\"variable\\\":\\\"--font-poppins\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"poppins\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_800_variable_font_poppins_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_800_variable_font_poppins_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/constants */ \"(rsc)/./lib/constants.ts\");\n\n\n\n\n\n\nconst metadata = {\n    title: {\n        default: `${_lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.name} - ${_lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.tagline}`,\n        template: `%s | ${_lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.name}`\n    },\n    description: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.description,\n    keywords: [\n        \"educational tours\",\n        \"student travel\",\n        \"experiential learning\",\n        \"adventure camps\",\n        \"school trips\",\n        \"Gujarat tourism\",\n        \"positive7\",\n        \"educational trips\",\n        \"student tours\",\n        \"CAS projects\",\n        \"workshops\",\n        \"picnics\"\n    ],\n    authors: [\n        {\n            name: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.name,\n            url: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.website\n        }\n    ],\n    creator: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.name,\n    publisher: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.name,\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL(_lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.website),\n    alternates: {\n        canonical: \"/\"\n    },\n    openGraph: {\n        type: \"website\",\n        locale: \"en_IN\",\n        url: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.website,\n        siteName: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.name,\n        title: `${_lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.name} - ${_lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.tagline}`,\n        description: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.description,\n        images: [\n            {\n                url: \"/images/og-image.jpg\",\n                width: 1200,\n                height: 630,\n                alt: `${_lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.name} - Educational Tours & Student Travel`\n            }\n        ]\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: `${_lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.name} - ${_lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.tagline}`,\n        description: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.description,\n        images: [\n            \"/images/twitter-image.jpg\"\n        ],\n        creator: \"@positive7ind\"\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    verification: {\n        google: \"your-google-verification-code\"\n    },\n    category: \"travel\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_800_variable_font_poppins_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_5___default().variable)}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\",\n                        sizes: \"any\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/icon.svg\",\n                        type: \"image/svg+xml\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#0ea5e9\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1, maximum-scale=5\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"font-sans antialiased\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex min-h-screen flex-col\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        type: \"application/ld+json\",\n                        dangerouslySetInnerHTML: {\n                            __html: JSON.stringify({\n                                \"@context\": \"https://schema.org\",\n                                \"@type\": \"TravelAgency\",\n                                name: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.name,\n                                description: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.description,\n                                url: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.website,\n                                logo: `${_lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.website}/images/positive7-logo.png`,\n                                image: `${_lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.website}/images/og-image.jpg`,\n                                telephone: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.phone,\n                                email: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.email,\n                                address: {\n                                    \"@type\": \"PostalAddress\",\n                                    streetAddress: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.address,\n                                    addressLocality: \"Ahmedabad\",\n                                    addressRegion: \"Gujarat\",\n                                    postalCode: \"380015\",\n                                    addressCountry: \"IN\"\n                                },\n                                sameAs: [\n                                    \"https://www.facebook.com/positive7.ind\",\n                                    \"https://www.instagram.com/positive.seven/\",\n                                    \"https://www.youtube.com/channel/UC22w2efe7oZCmEcrU8g2xnw/featured\"\n                                ],\n                                serviceType: [\n                                    \"Educational Tours\",\n                                    \"Student Travel\",\n                                    \"Adventure Camps\",\n                                    \"Experiential Learning\",\n                                    \"School Trips\",\n                                    \"CAS Projects\",\n                                    \"Workshops\"\n                                ],\n                                areaServed: {\n                                    \"@type\": \"Country\",\n                                    name: \"India\"\n                                }\n                            })\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./contexts/AuthContext.tsx":
/*!**********************************!*\
  !*** ./contexts/AuthContext.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e1),
/* harmony export */   useAuth: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\projects\p7-comprehensive\contexts\AuthContext.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\projects\p7-comprehensive\contexts\AuthContext.tsx#useAuth`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\projects\p7-comprehensive\contexts\AuthContext.tsx#AuthProvider`);


/***/ }),

/***/ "(rsc)/./lib/constants.ts":
/*!**************************!*\
  !*** ./lib/constants.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BOOKING_STATUS: () => (/* binding */ BOOKING_STATUS),\n/* harmony export */   COMPANY_INFO: () => (/* binding */ COMPANY_INFO),\n/* harmony export */   CONTACT_FORM_TYPES: () => (/* binding */ CONTACT_FORM_TYPES),\n/* harmony export */   DESTINATIONS: () => (/* binding */ DESTINATIONS),\n/* harmony export */   EDUCATIONAL_EXCELLENCE: () => (/* binding */ EDUCATIONAL_EXCELLENCE),\n/* harmony export */   FEATURED_TRIPS: () => (/* binding */ FEATURED_TRIPS),\n/* harmony export */   NAVIGATION_ITEMS: () => (/* binding */ NAVIGATION_ITEMS),\n/* harmony export */   QUICK_LINKS: () => (/* binding */ QUICK_LINKS),\n/* harmony export */   SAINT_AUGUSTINE_QUOTE: () => (/* binding */ SAINT_AUGUSTINE_QUOTE),\n/* harmony export */   SOCIAL_LINKS: () => (/* binding */ SOCIAL_LINKS),\n/* harmony export */   TESTIMONIALS: () => (/* binding */ TESTIMONIALS),\n/* harmony export */   TRIP_CATEGORIES: () => (/* binding */ TRIP_CATEGORIES),\n/* harmony export */   TRIP_DIFFICULTIES: () => (/* binding */ TRIP_DIFFICULTIES),\n/* harmony export */   UDBHAV_INFO: () => (/* binding */ UDBHAV_INFO),\n/* harmony export */   USER_ROLES: () => (/* binding */ USER_ROLES)\n/* harmony export */ });\n// Constants based on scraped content from positive7.in\nconst COMPANY_INFO = {\n    name: \"Positive7\",\n    tagline: \"Bring Learning To Life\",\n    heroQuote: \"The Best Way To Be Lost & Found At The Same Time Is To TRAVEL\",\n    description: \"Positive7 is a Gujarat Tourism affiliated outbound experiential learning company organizing, educational trips, students tour, CAS Projects, Picnics, adventure camps & Workshops.\",\n    address: \"904, SHIVALIK HIGHSTREET, LANDMARK: B/S, ITC NARMADA HOTEL MANSI – KESHAVBAUG ROAD ,VASTRAPUR, AHMEDABAD-380015.\",\n    phone: \"+91 78780 05500\",\n    alternatePhone: \"+91 7265005500\",\n    email: \"<EMAIL>\",\n    whatsapp: \"+917878005500\",\n    website: \"https://positive7.in\",\n    logo: \"/images/positive7-logo.png\"\n};\nconst SOCIAL_LINKS = {\n    facebook: \"https://www.facebook.com/positive7.ind\",\n    instagram: \"https://www.instagram.com/positive.seven/\",\n    youtube: \"https://www.youtube.com/channel/UC22w2efe7oZCmEcrU8g2xnw/featured\",\n    whatsapp: \"http://wa.me/+917878005500?text=Hi%20i%20have%20enquiry\"\n};\nconst NAVIGATION_ITEMS = [\n    {\n        name: \"Home\",\n        href: \"/\"\n    },\n    {\n        name: \"About\",\n        href: \"/about\"\n    },\n    {\n        name: \"Services\",\n        href: \"/services\"\n    },\n    {\n        name: \"Trips\",\n        href: \"/trips\"\n    },\n    {\n        name: \"Gallery\",\n        href: \"/gallery\"\n    },\n    {\n        name: \"Blog\",\n        href: \"/blog\"\n    },\n    {\n        name: \"Contact\",\n        href: \"/contact\"\n    },\n    {\n        name: \"Udbhav\",\n        href: \"/udbhav\"\n    }\n];\nconst QUICK_LINKS = [\n    {\n        name: \"About Us\",\n        href: \"/about\"\n    },\n    {\n        name: \"Gallery\",\n        href: \"/gallery\"\n    },\n    {\n        name: \"Blog\",\n        href: \"/blog\"\n    },\n    {\n        name: \"Trips Photos\",\n        href: \"/trips-photos\"\n    },\n    {\n        name: \"Terms & Conditions\",\n        href: \"/terms-conditions\"\n    },\n    {\n        name: \"Privacy Policy\",\n        href: \"/privacy-policy\"\n    }\n];\n// Scraped trip data from positive7.in\nconst FEATURED_TRIPS = [\n    {\n        id: \"manali\",\n        title: \"Manali\",\n        duration: \"9 Days 8 Nights\",\n        description: \"The most popular hill stations in Himachal, Manali offers the most magnificent views of the Pir Panjal and the Dhauladhar ranges covered with snow for most of the year.\",\n        image: \"https://positive7.in/wp-content/uploads/2025/01/gettyimages-**********-612x612-1.jpg\",\n        difficulty: \"moderate\",\n        category: \"Hill Station\",\n        destination: \"Himachal Pradesh\"\n    },\n    {\n        id: \"rishikesh\",\n        title: \"Rishikesh\",\n        duration: \"7 Days 6 Nights\",\n        description: 'Rishikesh, nestled in the foothills of the Himalayas along the banks of the Ganges River, is a captivating destination known as the \"Yoga Capital of the World\"',\n        image: \"https://positive7.in/wp-content/uploads/2022/09/dusk-time-rishikesh-holy-town-travel-destination-india-1024x684.jpg\",\n        difficulty: \"easy\",\n        category: \"Spiritual\",\n        destination: \"Uttarakhand\"\n    },\n    {\n        id: \"tirthan-valley\",\n        title: \"Tirthan Valley & Jibhi\",\n        duration: \"9 Days 8 Nights\",\n        description: \"Tirthan Valley: Serene Himalayan retreat with lush landscapes and access to the Great Himalayan National Park.\",\n        image: \"https://positive7.in/wp-content/uploads/2024/11/TIRTHAN-VALLEY-JIBHI-1024x697.webp\",\n        difficulty: \"moderate\",\n        category: \"Nature\",\n        destination: \"Himachal Pradesh\"\n    },\n    {\n        id: \"dharamshala\",\n        title: \"Dharamshala\",\n        duration: \"10 Days 9 Nights\",\n        description: \"Amritsar offers culture and history, Dharamshala provides Tibetan serenity, and Dalhousie delights with colonial charm and scenic beauty.\",\n        image: \"https://positive7.in/wp-content/uploads/2024/11/AMRITSAR-DHARAMSHALA-MCLEODGANJ-TRIUND-DALHOUSIE.webp\",\n        difficulty: \"moderate\",\n        category: \"Cultural\",\n        destination: \"Punjab & Himachal Pradesh\"\n    },\n    {\n        id: \"rajpura\",\n        title: \"Rajpura\",\n        duration: \"3 Days 2 Nights\",\n        description: \"Sundha Mata (Rajpura) is a small village located in Jalore district of Rajasthan. It is 64 km away from Mount Abu. This place is famous for Sundha Mata temple.\",\n        image: \"https://positive7.in/wp-content/uploads/2025/04/1602740643_Rajasthan_Adventure_Resort1.webp\",\n        difficulty: \"easy\",\n        category: \"Religious\",\n        destination: \"Rajasthan\"\n    },\n    {\n        id: \"brigu-lake\",\n        title: \"Brigu Lake\",\n        duration: \"9 Days 8 Nights\",\n        description: \"The Brigu Lake trek, located near Manali in Himachal Pradesh, is a stunning adventure that takes you through lush forests, picturesque meadows, and breathtaking mountain views.\",\n        image: \"https://positive7.in/wp-content/uploads/2024/11/BRIGU-LAKE2.webp\",\n        difficulty: \"challenging\",\n        category: \"Trekking\",\n        destination: \"Himachal Pradesh\"\n    }\n];\n// Scraped testimonials from positive7.in\nconst TESTIMONIALS = [\n    {\n        id: 1,\n        name: \"Krupa Bhatt\",\n        role: \"Student\",\n        content: \"If i could rewind those moments those days those experiences again then I surely would may it get less adventures may it get less thrilling and fun but still I would because the past 6 days were a whole sum of adventure and an unforgettable piece of my life.\",\n        rating: 5,\n        image: \"/images/testimonials/krupa-bhatt.jpg\"\n    },\n    {\n        id: 2,\n        name: \"Kavita Pillai\",\n        role: \"Parent\",\n        content: \"Trekking transforms lives, I had heard this, but for me, I can see those changes in my Son, he has impacted greatly, The transformations has been profound, he loved the trekking experience of his camping trip to Manali with Team Positive 7\",\n        rating: 5,\n        image: \"/images/testimonials/kavita-pillai.jpg\"\n    },\n    {\n        id: 3,\n        name: \"Hetal Vora\",\n        role: \"Parent\",\n        content: \"Kids had fun. The coordinators, arrangements, activities, stay place was planned where kids can have maximum enjoyment. Definitely recommended even kid has never stay away from parents for a day.\",\n        rating: 5,\n        image: \"/images/testimonials/hetal-vora.jpg\"\n    },\n    {\n        id: 4,\n        name: \"Sachin Mehta\",\n        role: \"Parent\",\n        content: \"Positive7 is a place of positivity and encouragement. The trip is well organized and has comfortable journey throughout. The activities are very enthusiastic and cheering and constant updates are given to the parents about the trip and the children.\",\n        rating: 5,\n        image: \"/images/testimonials/sachin-mehta.jpg\"\n    },\n    {\n        id: 5,\n        name: \"Rani Jaiswal\",\n        role: \"Educator\",\n        content: \"It is a Positive group that spreads positivity in the lives of people connected with it. A wonderful group that gave me beautiful moments to cherish in my life. I got one such good opportunity to be with them during our schl trip with our Student at Borsad, camp dilly.\",\n        rating: 5,\n        image: \"/images/testimonials/rani-jaiswal.jpg\"\n    },\n    {\n        id: 6,\n        name: \"Shirali Shah\",\n        role: \"Parent\",\n        content: \"Positive7 is such a wonderful team and great example of super team work. Super experience and lot's of fun with discipline. My son learn so much new things. I have send my son for the first time with you and the experience was awesome. Thank you so much.\",\n        rating: 5,\n        image: \"/images/testimonials/shirali-shah.jpg\"\n    }\n];\nconst TRIP_CATEGORIES = [\n    \"Hill Station\",\n    \"Spiritual\",\n    \"Nature\",\n    \"Cultural\",\n    \"Religious\",\n    \"Trekking\",\n    \"Adventure\",\n    \"Wildlife\",\n    \"Historical\"\n];\nconst TRIP_DIFFICULTIES = [\n    {\n        value: \"easy\",\n        label: \"Easy\",\n        color: \"green\"\n    },\n    {\n        value: \"moderate\",\n        label: \"Moderate\",\n        color: \"yellow\"\n    },\n    {\n        value: \"challenging\",\n        label: \"Challenging\",\n        color: \"orange\"\n    },\n    {\n        value: \"extreme\",\n        label: \"Extreme\",\n        color: \"red\"\n    }\n];\nconst DESTINATIONS = [\n    \"Himachal Pradesh\",\n    \"Uttarakhand\",\n    \"Rajasthan\",\n    \"Punjab\",\n    \"Gujarat\",\n    \"Maharashtra\",\n    \"Goa\",\n    \"Kerala\",\n    \"Karnataka\",\n    \"Tamil Nadu\"\n];\nconst UDBHAV_INFO = {\n    title: \"Udbhav: Exploring Rural Life\",\n    description: 'Taking inspiration from these quotes we at \"Positive7\" have come up with an initiative known as \"Udbhav\". It will be a drive to connect the rural and urban areas through culture, art and traditions.',\n    images: [\n        \"https://positive7.in/wp-content/uploads/2022/07/Udbhav.jpg\",\n        \"https://positive7.in/wp-content/uploads/2022/07/Udbhav-2-scaled.jpg\",\n        \"https://positive7.in/wp-content/uploads/2022/07/Udbhav-1-scaled.jpg\",\n        \"https://positive7.in/wp-content/uploads/2022/07/Udbhav-3-1024x467.jpg\"\n    ]\n};\nconst SAINT_AUGUSTINE_QUOTE = {\n    text: '\"The world is a book, and those who do not travel read only one page.\"',\n    author: \"Saint Augustine\",\n    image: \"https://positive7.in/wp-content/uploads/2018/11/quote-1.png\"\n};\nconst EDUCATIONAL_EXCELLENCE = {\n    title: \"Educational Excellence\",\n    description: \"We believe it's not just the exposure to new places that changes student's lives, but also the kind of experience they have during that exposure. That's why we work with you to build programme content that meets your travel / learning goals.\"\n};\nconst CONTACT_FORM_TYPES = [\n    \"General Inquiry\",\n    \"Trip Booking\",\n    \"Custom Trip Request\",\n    \"Group Booking\",\n    \"Educational Program\",\n    \"Udbhav Initiative\",\n    \"Partnership\",\n    \"Other\"\n];\nconst BOOKING_STATUS = {\n    PENDING: \"pending\",\n    CONFIRMED: \"confirmed\",\n    CANCELLED: \"cancelled\",\n    COMPLETED: \"completed\"\n};\nconst USER_ROLES = {\n    CUSTOMER: \"customer\",\n    ADMIN: \"admin\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/constants.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();