'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Cloud, 
  Sun, 
  CloudRain, 
  CloudSnow, 
  Wind, 
  Thermometer, 
  Droplets, 
  Eye,
  Sunrise,
  Sunset,
  MapPin,
  Calendar,
  AlertTriangle,
  TrendingUp,
  TrendingDown
} from 'lucide-react'

interface WeatherData {
  location: string
  current: {
    temperature: number
    condition: string
    humidity: number
    windSpeed: number
    visibility: number
    uvIndex: number
    feelsLike: number
    icon: string
  }
  forecast: Array<{
    date: string
    day: string
    high: number
    low: number
    condition: string
    icon: string
    precipitation: number
  }>
  alerts?: Array<{
    type: 'warning' | 'advisory' | 'watch'
    title: string
    description: string
  }>
  bestTimeToVisit?: string
  travelTips?: string[]
}

interface WeatherWidgetProps {
  destinations: string[]
  selectedDestination?: string
  onDestinationChange?: (destination: string) => void
}

// Mock weather data based on popular Positive7 destinations
const WEATHER_DATA: Record<string, WeatherData> = {
  'Manali': {
    location: 'Manali, Himachal Pradesh',
    current: {
      temperature: 15,
      condition: 'Partly Cloudy',
      humidity: 65,
      windSpeed: 12,
      visibility: 10,
      uvIndex: 6,
      feelsLike: 13,
      icon: 'partly-cloudy'
    },
    forecast: [
      { date: '2024-02-15', day: 'Today', high: 18, low: 8, condition: 'Sunny', icon: 'sunny', precipitation: 0 },
      { date: '2024-02-16', day: 'Tomorrow', high: 16, low: 6, condition: 'Partly Cloudy', icon: 'partly-cloudy', precipitation: 10 },
      { date: '2024-02-17', day: 'Sat', high: 14, low: 4, condition: 'Snow', icon: 'snow', precipitation: 80 },
      { date: '2024-02-18', day: 'Sun', high: 12, low: 2, condition: 'Snow', icon: 'snow', precipitation: 90 },
      { date: '2024-02-19', day: 'Mon', high: 15, low: 5, condition: 'Cloudy', icon: 'cloudy', precipitation: 20 },
      { date: '2024-02-20', day: 'Tue', high: 17, low: 7, condition: 'Sunny', icon: 'sunny', precipitation: 0 },
      { date: '2024-02-21', day: 'Wed', high: 19, low: 9, condition: 'Sunny', icon: 'sunny', precipitation: 0 }
    ],
    alerts: [
      {
        type: 'advisory',
        title: 'Snow Advisory',
        description: 'Light to moderate snowfall expected over the weekend. Carry warm clothing and snow gear.'
      }
    ],
    bestTimeToVisit: 'March to June and September to November',
    travelTips: [
      'Pack warm clothes and snow gear',
      'Best time for snow activities: December to February',
      'Carry sunscreen due to high altitude UV exposure',
      'Book accommodations in advance during peak season'
    ]
  },
  'Rishikesh': {
    location: 'Rishikesh, Uttarakhand',
    current: {
      temperature: 22,
      condition: 'Clear',
      humidity: 55,
      windSpeed: 8,
      visibility: 15,
      uvIndex: 7,
      feelsLike: 24,
      icon: 'sunny'
    },
    forecast: [
      { date: '2024-02-15', day: 'Today', high: 25, low: 12, condition: 'Sunny', icon: 'sunny', precipitation: 0 },
      { date: '2024-02-16', day: 'Tomorrow', high: 26, low: 13, condition: 'Sunny', icon: 'sunny', precipitation: 0 },
      { date: '2024-02-17', day: 'Sat', high: 24, low: 11, condition: 'Partly Cloudy', icon: 'partly-cloudy', precipitation: 5 },
      { date: '2024-02-18', day: 'Sun', high: 23, low: 10, condition: 'Cloudy', icon: 'cloudy', precipitation: 15 },
      { date: '2024-02-19', day: 'Mon', high: 25, low: 12, condition: 'Sunny', icon: 'sunny', precipitation: 0 },
      { date: '2024-02-20', day: 'Tue', high: 27, low: 14, condition: 'Sunny', icon: 'sunny', precipitation: 0 },
      { date: '2024-02-21', day: 'Wed', high: 28, low: 15, condition: 'Sunny', icon: 'sunny', precipitation: 0 }
    ],
    bestTimeToVisit: 'September to April',
    travelTips: [
      'Perfect weather for river rafting and outdoor activities',
      'Carry light cotton clothes and a light jacket for evenings',
      'Stay hydrated and use sunscreen',
      'Best time for yoga and meditation retreats'
    ]
  },
  'Dharamshala': {
    location: 'Dharamshala, Himachal Pradesh',
    current: {
      temperature: 18,
      condition: 'Cloudy',
      humidity: 70,
      windSpeed: 10,
      visibility: 8,
      uvIndex: 4,
      feelsLike: 16,
      icon: 'cloudy'
    },
    forecast: [
      { date: '2024-02-15', day: 'Today', high: 20, low: 10, condition: 'Cloudy', icon: 'cloudy', precipitation: 30 },
      { date: '2024-02-16', day: 'Tomorrow', high: 18, low: 8, condition: 'Light Rain', icon: 'rain', precipitation: 60 },
      { date: '2024-02-17', day: 'Sat', high: 16, low: 6, condition: 'Rain', icon: 'rain', precipitation: 80 },
      { date: '2024-02-18', day: 'Sun', high: 19, low: 9, condition: 'Partly Cloudy', icon: 'partly-cloudy', precipitation: 20 },
      { date: '2024-02-19', day: 'Mon', high: 21, low: 11, condition: 'Sunny', icon: 'sunny', precipitation: 0 },
      { date: '2024-02-20', day: 'Tue', high: 22, low: 12, condition: 'Sunny', icon: 'sunny', precipitation: 0 },
      { date: '2024-02-21', day: 'Wed', high: 20, low: 10, condition: 'Partly Cloudy', icon: 'partly-cloudy', precipitation: 10 }
    ],
    bestTimeToVisit: 'March to June and September to December',
    travelTips: [
      'Carry rain gear and warm clothes',
      'Perfect for exploring Tibetan culture and monasteries',
      'Great trekking weather after the rain clears',
      'Visit McLeod Ganj for the Dalai Lama temple'
    ]
  }
}

const weatherIcons = {
  'sunny': Sun,
  'partly-cloudy': Cloud,
  'cloudy': Cloud,
  'rain': CloudRain,
  'snow': CloudSnow
}

export function WeatherWidget({ 
  destinations, 
  selectedDestination = destinations[0],
  onDestinationChange 
}: WeatherWidgetProps) {
  const [currentDestination, setCurrentDestination] = useState(selectedDestination)
  const [weatherData, setWeatherData] = useState<WeatherData | null>(null)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    fetchWeatherData(currentDestination)
  }, [currentDestination])

  const fetchWeatherData = async (destination: string) => {
    setLoading(true)
    // Simulate API call delay
    setTimeout(() => {
      setWeatherData(WEATHER_DATA[destination] || null)
      setLoading(false)
    }, 500)
  }

  const handleDestinationChange = (destination: string) => {
    setCurrentDestination(destination)
    onDestinationChange?.(destination)
  }

  const getWeatherIcon = (iconName: string) => {
    const IconComponent = weatherIcons[iconName as keyof typeof weatherIcons] || Cloud
    return IconComponent
  }

  const getAlertIcon = (type: string) => {
    return AlertTriangle
  }

  const getAlertColor = (type: string) => {
    switch (type) {
      case 'warning': return 'text-red-600 bg-red-50 border-red-200'
      case 'advisory': return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'watch': return 'text-blue-600 bg-blue-50 border-blue-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  if (loading) {
    return (
      <div className="bg-white rounded-2xl p-8 shadow-lg">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="h-20 bg-gray-200 rounded mb-4"></div>
          <div className="grid grid-cols-7 gap-2">
            {[...Array(7)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (!weatherData) {
    return (
      <div className="bg-white rounded-2xl p-8 shadow-lg text-center">
        <div className="text-gray-500">
          <Cloud className="w-12 h-12 mx-auto mb-4" />
          <p>Weather data not available for this destination</p>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-2xl p-8 shadow-lg">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Weather Forecast</h2>
        <select
          value={currentDestination}
          onChange={(e) => handleDestinationChange(e.target.value)}
          className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          {destinations.map((destination) => (
            <option key={destination} value={destination}>
              {destination}
            </option>
          ))}
        </select>
      </div>

      {/* Current Weather */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-8"
      >
        <div className="flex items-center gap-2 mb-4">
          <MapPin className="w-4 h-4 text-gray-500" />
          <span className="text-gray-600">{weatherData.location}</span>
        </div>
        
        <div className="grid md:grid-cols-2 gap-6">
          {/* Main Weather */}
          <div className="flex items-center gap-6">
            <div className="w-20 h-20 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full flex items-center justify-center">
              {React.createElement(getWeatherIcon(weatherData.current.icon), {
                className: "w-10 h-10 text-blue-600"
              })}
            </div>
            <div>
              <div className="text-4xl font-bold text-gray-900">
                {weatherData.current.temperature}°C
              </div>
              <div className="text-gray-600">{weatherData.current.condition}</div>
              <div className="text-sm text-gray-500">
                Feels like {weatherData.current.feelsLike}°C
              </div>
            </div>
          </div>

          {/* Weather Details */}
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center gap-2">
              <Droplets className="w-4 h-4 text-blue-500" />
              <div>
                <div className="text-sm text-gray-500">Humidity</div>
                <div className="font-medium">{weatherData.current.humidity}%</div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Wind className="w-4 h-4 text-gray-500" />
              <div>
                <div className="text-sm text-gray-500">Wind</div>
                <div className="font-medium">{weatherData.current.windSpeed} km/h</div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Eye className="w-4 h-4 text-gray-500" />
              <div>
                <div className="text-sm text-gray-500">Visibility</div>
                <div className="font-medium">{weatherData.current.visibility} km</div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Sun className="w-4 h-4 text-yellow-500" />
              <div>
                <div className="text-sm text-gray-500">UV Index</div>
                <div className="font-medium">{weatherData.current.uvIndex}</div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Weather Alerts */}
      {weatherData.alerts && weatherData.alerts.length > 0 && (
        <div className="mb-6">
          {weatherData.alerts.map((alert, index) => {
            const AlertIcon = getAlertIcon(alert.type)
            return (
              <div
                key={index}
                className={`p-4 rounded-lg border ${getAlertColor(alert.type)} mb-2 last:mb-0`}
              >
                <div className="flex items-start gap-3">
                  <AlertIcon className="w-5 h-5 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-medium mb-1">{alert.title}</h4>
                    <p className="text-sm">{alert.description}</p>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      )}

      {/* 7-Day Forecast */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <Calendar className="w-5 h-5" />
          7-Day Forecast
        </h3>
        <div className="grid grid-cols-7 gap-2">
          {weatherData.forecast.map((day, index) => {
            const WeatherIcon = getWeatherIcon(day.icon)
            return (
              <div
                key={day.date}
                className={`p-3 rounded-lg text-center ${
                  index === 0 ? 'bg-blue-50 border border-blue-200' : 'bg-gray-50'
                }`}
              >
                <div className="text-sm font-medium text-gray-900 mb-2">
                  {day.day}
                </div>
                <div className="w-8 h-8 mx-auto mb-2 flex items-center justify-center">
                  <WeatherIcon className="w-6 h-6 text-blue-600" />
                </div>
                <div className="text-xs text-gray-600 mb-1">{day.condition}</div>
                <div className="flex items-center justify-center gap-1 text-xs">
                  <span className="font-medium">{day.high}°</span>
                  <span className="text-gray-500">{day.low}°</span>
                </div>
                {day.precipitation > 0 && (
                  <div className="text-xs text-blue-600 mt-1">
                    {day.precipitation}%
                  </div>
                )}
              </div>
            )
          })}
        </div>
      </div>

      {/* Travel Information */}
      <div className="grid md:grid-cols-2 gap-6">
        {/* Best Time to Visit */}
        <div className="p-4 bg-green-50 rounded-lg border border-green-200">
          <h4 className="font-medium text-green-900 mb-2 flex items-center gap-2">
            <Calendar className="w-4 h-4" />
            Best Time to Visit
          </h4>
          <p className="text-sm text-green-800">{weatherData.bestTimeToVisit}</p>
        </div>

        {/* Travel Tips */}
        {weatherData.travelTips && (
          <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
            <h4 className="font-medium text-blue-900 mb-2 flex items-center gap-2">
              <Thermometer className="w-4 h-4" />
              Travel Tips
            </h4>
            <ul className="text-sm text-blue-800 space-y-1">
              {weatherData.travelTips.slice(0, 2).map((tip, index) => (
                <li key={index} className="flex items-start gap-1">
                  <span className="text-blue-600 mt-1">•</span>
                  <span>{tip}</span>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </div>
  )
}
