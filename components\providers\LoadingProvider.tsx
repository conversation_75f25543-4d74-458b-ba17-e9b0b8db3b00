'use client'

import { createContext, useContext, useState, ReactNode } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

interface LoadingContextType {
  isLoading: boolean
  loadingMessage: string
  showLoading: (message?: string) => void
  hideLoading: () => void
  setLoadingMessage: (message: string) => void
}

const LoadingContext = createContext<LoadingContextType | undefined>(undefined)

export function useLoading() {
  const context = useContext(LoadingContext)
  if (context === undefined) {
    throw new Error('useLoading must be used within a LoadingProvider')
  }
  return context
}

interface LoadingProviderProps {
  children: ReactNode
}

export function LoadingProvider({ children }: LoadingProviderProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [loadingMessage, setLoadingMessage] = useState('Loading...')

  const showLoading = (message = 'Loading...') => {
    setLoadingMessage(message)
    setIsLoading(true)
  }

  const hideLoading = () => {
    setIsLoading(false)
  }

  const updateLoadingMessage = (message: string) => {
    setLoadingMessage(message)
  }

  return (
    <LoadingContext.Provider
      value={{
        isLoading,
        loadingMessage,
        showLoading,
        hideLoading,
        setLoadingMessage: updateLoadingMessage
      }}
    >
      {children}
      
      {/* Global Loading Overlay */}
      <AnimatePresence>
        {isLoading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm flex items-center justify-center"
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              className="bg-white rounded-2xl p-8 shadow-2xl max-w-sm w-full mx-4"
            >
              <div className="text-center">
                <LoadingSpinner size="xl" className="mx-auto mb-6" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {loadingMessage}
                </h3>
                <p className="text-gray-600 text-sm">
                  Please wait while we process your request
                </p>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </LoadingContext.Provider>
  )
}

// Hook for API calls with loading states
export function useAsyncOperation() {
  const { showLoading, hideLoading, setLoadingMessage } = useLoading()

  const executeAsync = async <T,>(
    operation: () => Promise<T>,
    loadingMessage = 'Processing...'
  ): Promise<T> => {
    try {
      showLoading(loadingMessage)
      const result = await operation()
      return result
    } finally {
      hideLoading()
    }
  }

  const executeWithSteps = async <T,>(
    steps: Array<{
      operation: () => Promise<any>
      message: string
    }>
  ): Promise<T> => {
    try {
      let result: T
      
      for (let i = 0; i < steps.length; i++) {
        const step = steps[i]
        setLoadingMessage(`${step.message} (${i + 1}/${steps.length})`)
        
        if (!showLoading) {
          showLoading(step.message)
        }
        
        result = await step.operation()
      }
      
      return result!
    } finally {
      hideLoading()
    }
  }

  return {
    executeAsync,
    executeWithSteps
  }
}

// Component wrapper for loading states
export function WithLoading<T extends object>({
  isLoading,
  loadingComponent,
  errorComponent,
  error,
  children
}: {
  isLoading: boolean
  loadingComponent?: ReactNode
  errorComponent?: ReactNode
  error?: Error | null
  children: ReactNode
}) {
  if (error && errorComponent) {
    return <>{errorComponent}</>
  }

  if (isLoading) {
    return loadingComponent || (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <LoadingSpinner size="lg" className="mx-auto mb-4" />
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  return <>{children}</>
}

// Higher-order component for automatic loading states
export function withLoadingState<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  loadingComponent?: ReactNode
) {
  return function LoadingWrapper(props: P & { isLoading?: boolean; error?: Error }) {
    const { isLoading = false, error, ...restProps } = props

    return (
      <WithLoading
        isLoading={isLoading}
        error={error}
        loadingComponent={loadingComponent}
        errorComponent={
          error ? (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Something went wrong</h3>
              <p className="text-gray-600 mb-6">{error.message}</p>
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Try Again
              </button>
            </div>
          ) : undefined
        }
      >
        <WrappedComponent {...(restProps as P)} />
      </WithLoading>
    )
  }
}
