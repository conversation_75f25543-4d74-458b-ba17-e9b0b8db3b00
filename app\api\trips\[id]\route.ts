import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase';
import { verifySession } from '@/lib/supabase';
import type { CreateTripData } from '@/types/database';

interface RouteParams {
  params: {
    id: string;
  };
}

// GET /api/trips/[id] - Get a specific trip
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const { id } = params;
    const supabase = createServerSupabase();

    // Check if ID is a UUID or slug
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id);
    
    let query = supabase
      .from('trips')
      .select(`
        *,
        trip_images(*),
        testimonials(
          id,
          name,
          rating,
          title,
          content,
          image_url,
          created_at
        )
      `);

    if (isUUID) {
      query = query.eq('id', id);
    } else {
      query = query.eq('slug', id);
    }

    const { data: trip, error } = await query.single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Trip not found' },
          { status: 404 }
        );
      }
      console.error('Error fetching trip:', error);
      return NextResponse.json(
        { error: 'Failed to fetch trip' },
        { status: 500 }
      );
    }

    // Check if trip is active (unless user is admin)
    const session = await verifySession();
    const isAdmin = session && await supabase
      .from('users')
      .select('role')
      .eq('id', session.user.id)
      .single()
      .then(({ data }) => data?.role === 'admin');

    if (!trip.is_active && !isAdmin) {
      return NextResponse.json(
        { error: 'Trip not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      data: trip,
    });
  } catch (error) {
    console.error('Error in GET /api/trips/[id]:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/trips/[id] - Update a trip (Admin only)
export async function PUT(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await verifySession('admin');
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = params;
    const body: Partial<CreateTripData> = await request.json();
    const supabase = createServerSupabase();

    // Check if trip exists
    const { data: existingTrip, error: fetchError } = await supabase
      .from('trips')
      .select('id, slug')
      .eq('id', id)
      .single();

    if (fetchError || !existingTrip) {
      return NextResponse.json(
        { error: 'Trip not found' },
        { status: 404 }
      );
    }

    // Check if slug is unique (if being updated)
    if (body.slug && body.slug !== existingTrip.slug) {
      const { data: slugExists } = await supabase
        .from('trips')
        .select('id')
        .eq('slug', body.slug)
        .neq('id', id)
        .single();

      if (slugExists) {
        return NextResponse.json(
          { error: 'Trip with this slug already exists' },
          { status: 400 }
        );
      }
    }

    // Update trip
    const { data: trip, error } = await supabase
      .from('trips')
      .update({
        ...body,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating trip:', error);
      return NextResponse.json(
        { error: 'Failed to update trip' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      data: trip,
      message: 'Trip updated successfully',
    });
  } catch (error) {
    console.error('Error in PUT /api/trips/[id]:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/trips/[id] - Delete a trip (Admin only)
export async function DELETE(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await verifySession('admin');
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = params;
    const supabase = createServerSupabase();

    // Check if trip has any bookings
    const { data: bookings, error: bookingsError } = await supabase
      .from('bookings')
      .select('id')
      .eq('trip_id', id)
      .limit(1);

    if (bookingsError) {
      console.error('Error checking bookings:', bookingsError);
      return NextResponse.json(
        { error: 'Failed to check trip bookings' },
        { status: 500 }
      );
    }

    if (bookings && bookings.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete trip with existing bookings. Consider deactivating instead.' },
        { status: 400 }
      );
    }

    // Delete trip (this will cascade delete trip_images due to foreign key constraint)
    const { error } = await supabase
      .from('trips')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting trip:', error);
      return NextResponse.json(
        { error: 'Failed to delete trip' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: 'Trip deleted successfully',
    });
  } catch (error) {
    console.error('Error in DELETE /api/trips/[id]:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
