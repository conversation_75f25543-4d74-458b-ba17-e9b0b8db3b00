import type { Metadata } from 'next';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import HeroSection from '@/components/sections/HeroSection';
import FeaturedTripsSection from '@/components/sections/FeaturedTripsSection';
import AboutSection from '@/components/sections/AboutSection';
import TestimonialsSection from '@/components/sections/TestimonialsSection';
import ContactSection from '@/components/sections/ContactSection';
import { COMPANY_INFO } from '@/lib/constants';

export const metadata: Metadata = {
  title: `${COMPANY_INFO.name} - ${COMPANY_INFO.tagline}`,
  description: COMPANY_INFO.description,
  keywords: [
    'educational tours',
    'student travel',
    'experiential learning',
    'adventure camps',
    'school trips',
    'Gujarat tourism',
    'positive7',
    'Manali trips',
    'Rishikesh tours',
    'CAS projects',
    'workshops',
    'student adventures',
  ],
  openGraph: {
    title: `${COMPANY_INFO.name} - ${COMPANY_INFO.tagline}`,
    description: COMPANY_INFO.description,
    url: COMPANY_INFO.website,
    siteName: COMPANY_INFO.name,
    images: [
      {
        url: '/images/og-home.jpg',
        width: 1200,
        height: 630,
        alt: `${COMPANY_INFO.name} - Educational Tours & Student Travel`,
      },
    ],
    locale: 'en_IN',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: `${COMPANY_INFO.name} - ${COMPANY_INFO.tagline}`,
    description: COMPANY_INFO.description,
    images: ['/images/twitter-home.jpg'],
  },
  alternates: {
    canonical: '/',
  },
};

export default function HomePage() {
  return (
    <>
      <Header />
      <main className="flex-1">
        {/* Hero Section */}
        <HeroSection />
        
        {/* Featured Trips Section */}
        <FeaturedTripsSection />
        
        {/* About Section */}
        <AboutSection />
        
        {/* Testimonials Section */}
        <TestimonialsSection />
        
        {/* Contact Section */}
        <ContactSection />
      </main>
      <Footer />
      
      {/* Structured Data for Homepage */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'Organization',
            name: COMPANY_INFO.name,
            alternateName: 'Positive Seven',
            description: COMPANY_INFO.description,
            url: COMPANY_INFO.website,
            logo: `${COMPANY_INFO.website}/images/positive7-logo.png`,
            image: `${COMPANY_INFO.website}/images/og-home.jpg`,
            telephone: COMPANY_INFO.phone,
            email: COMPANY_INFO.email,
            address: {
              '@type': 'PostalAddress',
              streetAddress: '904, Shivalik Highstreet, B/S ITC Narmada Hotel',
              addressLocality: 'Vastrapur, Ahmedabad',
              addressRegion: 'Gujarat',
              postalCode: '380015',
              addressCountry: 'IN',
            },
            geo: {
              '@type': 'GeoCoordinates',
              latitude: '23.0225',
              longitude: '72.5714',
            },
            sameAs: [
              'https://www.facebook.com/positive7.ind',
              'https://www.instagram.com/positive.seven/',
              'https://www.youtube.com/channel/UC22w2efe7oZCmEcrU8g2xnw/featured',
            ],
            serviceType: [
              'Educational Tours',
              'Student Travel',
              'Adventure Camps',
              'Experiential Learning',
              'School Trips',
              'CAS Projects',
              'Workshops',
              'Picnics',
            ],
            areaServed: [
              {
                '@type': 'Country',
                name: 'India',
              },
            ],
            hasOfferCatalog: {
              '@type': 'OfferCatalog',
              name: 'Educational Tours and Student Travel Services',
              itemListElement: [
                {
                  '@type': 'Offer',
                  itemOffered: {
                    '@type': 'Service',
                    name: 'Manali Educational Tour',
                    description: 'Snow-capped peaks and adventure learning experience in Himachal Pradesh',
                  },
                },
                {
                  '@type': 'Offer',
                  itemOffered: {
                    '@type': 'Service',
                    name: 'Rishikesh Spiritual Tour',
                    description: 'Yoga and spiritual learning experience in the Yoga Capital of the World',
                  },
                },
                {
                  '@type': 'Offer',
                  itemOffered: {
                    '@type': 'Service',
                    name: 'Adventure Camps',
                    description: 'Outdoor adventure and team building camps for students',
                  },
                },
                {
                  '@type': 'Offer',
                  itemOffered: {
                    '@type': 'Service',
                    name: 'CAS Projects',
                    description: 'Creativity, Activity, Service projects for IB students',
                  },
                },
              ],
            },
            aggregateRating: {
              '@type': 'AggregateRating',
              ratingValue: '4.8',
              reviewCount: '150',
              bestRating: '5',
              worstRating: '1',
            },
            review: [
              {
                '@type': 'Review',
                author: {
                  '@type': 'Person',
                  name: 'Krupa Bhatt',
                },
                reviewRating: {
                  '@type': 'Rating',
                  ratingValue: '5',
                  bestRating: '5',
                },
                reviewBody: 'If i could rewind those moments those days those experiences again then I surely would may it get less adventures may it get less thrilling and fun but still I would because the past 6 days were a whole sum of adventure and an unforgettable piece of my life.',
              },
              {
                '@type': 'Review',
                author: {
                  '@type': 'Person',
                  name: 'Kavita Pillai',
                },
                reviewRating: {
                  '@type': 'Rating',
                  ratingValue: '5',
                  bestRating: '5',
                },
                reviewBody: 'Trekking transforms lives, I had heard this, but for me, I can see those changes in my Son, he has impacted greatly, The transformations has been profound, he loved the trekking experience of his camping trip to Manali with Team Positive 7',
              },
            ],
            founder: {
              '@type': 'Organization',
              name: COMPANY_INFO.name,
            },
            foundingDate: '2009',
            numberOfEmployees: {
              '@type': 'QuantitativeValue',
              value: '25',
            },
            slogan: COMPANY_INFO.tagline,
          }),
        }}
      />
    </>
  );
}
