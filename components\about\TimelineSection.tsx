'use client'

import { motion } from 'framer-motion'
import { 
  Calendar, 
  MapPin, 
  Users, 
  Award,
  Rocket,
  Globe,
  Star,
  BookOpen
} from 'lucide-react'

const TIMELINE_EVENTS = [
  {
    year: '2009',
    title: 'The Beginning',
    description: 'Positive7 was founded with a vision to transform education through experiential learning and travel.',
    icon: Rocket,
    color: 'from-blue-500 to-blue-600',
    achievements: [
      'First educational tour to Manali',
      '50 students from 3 schools',
      'Foundation of safety-first approach'
    ]
  },
  {
    year: '2012',
    title: 'Expanding Horizons',
    description: 'Introduced new destinations and specialized programs for different age groups and learning objectives.',
    icon: MapPin,
    color: 'from-green-500 to-green-600',
    achievements: [
      'Added Rishikesh and Dharamshala',
      '500+ students annually',
      'Partnership with 25+ schools'
    ]
  },
  {
    year: '2015',
    title: 'Innovation in Learning',
    description: 'Launched curriculum-aligned programs and introduced technology-enhanced learning experiences.',
    icon: BookOpen,
    color: 'from-purple-500 to-purple-600',
    achievements: [
      'Pre and post-trip learning modules',
      'Digital portfolios for students',
      'Teacher training workshops'
    ]
  },
  {
    year: '2018',
    title: 'Recognition & Growth',
    description: 'Received multiple industry awards and expanded to serve schools across Western India.',
    icon: Award,
    color: 'from-yellow-500 to-yellow-600',
    achievements: [
      'Gujarat Tourism Excellence Award',
      '100+ school partnerships',
      '10,000+ students impacted'
    ]
  },
  {
    year: '2020',
    title: 'Adapting to Change',
    description: 'Pivoted to virtual learning experiences during the pandemic while maintaining our commitment to education.',
    icon: Globe,
    color: 'from-teal-500 to-teal-600',
    achievements: [
      'Virtual cultural exchanges',
      'Online adventure challenges',
      'Digital storytelling workshops'
    ]
  },
  {
    year: '2022',
    title: 'Sustainable Tourism',
    description: 'Launched eco-friendly initiatives and community-based tourism programs.',
    icon: Users,
    color: 'from-green-600 to-emerald-600',
    achievements: [
      'Carbon-neutral trip options',
      'Local community partnerships',
      'Environmental education programs'
    ]
  },
  {
    year: '2024',
    title: 'Digital Transformation',
    description: 'Embracing technology to enhance the educational travel experience with our new digital platform.',
    icon: Star,
    color: 'from-indigo-500 to-purple-600',
    achievements: [
      'AI-powered trip recommendations',
      'Real-time parent updates',
      '50,000+ students milestone'
    ]
  }
]

export function TimelineSection() {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, x: -50 },
    visible: { opacity: 1, x: 0 }
  }

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">Our Journey</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            From humble beginnings to becoming a leader in educational tourism, 
            here's how we've grown and evolved over the years
          </p>
        </div>
        
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="relative"
        >
          {/* Timeline Line */}
          <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-blue-500 to-green-500 hidden lg:block" />
          
          <div className="space-y-12">
            {TIMELINE_EVENTS.map((event, index) => (
              <motion.div
                key={event.year}
                variants={itemVariants}
                className="relative"
              >
                {/* Timeline Marker */}
                <div className="absolute left-6 w-4 h-4 bg-gradient-to-r from-blue-600 to-green-600 rounded-full border-4 border-white shadow-lg z-10 hidden lg:block" />
                
                {/* Content Card */}
                <div className="lg:ml-16 bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300">
                  <div className="flex flex-col lg:flex-row gap-6">
                    {/* Icon and Year */}
                    <div className="flex-shrink-0">
                      <div className={`w-16 h-16 bg-gradient-to-r ${event.color} rounded-full flex items-center justify-center mb-4`}>
                        <event.icon className="w-8 h-8 text-white" />
                      </div>
                      <div className="text-center lg:text-left">
                        <div className="text-2xl font-bold text-gray-900">{event.year}</div>
                      </div>
                    </div>
                    
                    {/* Content */}
                    <div className="flex-1">
                      <h3 className="text-2xl font-bold text-gray-900 mb-3">{event.title}</h3>
                      <p className="text-gray-700 mb-6 leading-relaxed">{event.description}</p>
                      
                      {/* Achievements */}
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900 mb-3">Key Achievements:</h4>
                        <ul className="space-y-2">
                          {event.achievements.map((achievement, achievementIndex) => (
                            <li key={achievementIndex} className="flex items-start gap-3">
                              <div className="w-2 h-2 bg-gradient-to-r from-blue-600 to-green-600 rounded-full mt-2 flex-shrink-0" />
                              <span className="text-gray-700">{achievement}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Future Vision */}
        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-blue-600 to-green-600 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">Looking Ahead</h3>
            <p className="text-blue-100 max-w-3xl mx-auto leading-relaxed">
              As we continue our journey, we remain committed to innovation, safety, and educational excellence. 
              Our vision for the future includes expanding to new destinations, incorporating cutting-edge technology, 
              and continuing to transform lives through the power of educational travel.
            </p>
            <div className="flex flex-wrap justify-center gap-8 mt-8">
              <div className="text-center">
                <div className="text-3xl font-bold">2025</div>
                <div className="text-blue-100">International Programs</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">2026</div>
                <div className="text-blue-100">100,000 Students</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">2027</div>
                <div className="text-blue-100">Pan-India Presence</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
