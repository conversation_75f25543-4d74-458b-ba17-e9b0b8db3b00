import { Metadata } from 'next'
import Image from 'next/image'
import { motion } from 'framer-motion'
import { 
  Quote, 
  Award, 
  MapPin, 
  Calendar, 
  Users, 
  Heart,
  Lightbulb,
  Target,
  BookOpen,
  Mountain,
  Star,
  Globe
} from 'lucide-react'
import Button from '@/components/ui/Button'

export const metadata: Metadata = {
  title: '<PERSON><PERSON><PERSON><PERSON> - Founder & CEO | Positive7 Educational Tours',
  description: '<PERSON><PERSON>, the visionary founder of Positive7 who has transformed educational tourism in India. Learn about his journey, philosophy, and commitment to student development.',
  keywords: '<PERSON><PERSON><PERSON><PERSON>, Positive7 founder, educational tourism leader, student development, experiential learning',
  openGraph: {
    title: '<PERSON><PERSON><PERSON><PERSON> Patel - Transforming Education Through Travel',
    description: 'Discover the story of Positive7\'s founder and his mission to create life-changing educational experiences.',
    images: ['/images/udbhav-hero.jpg'],
    type: 'profile'
  }
}

export default function UdbhavPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-green-600/10" />
        <div className="max-w-7xl mx-auto px-4 relative">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
                <Users className="w-4 h-4" />
                Founder & CEO
              </div>
              <h1 className="text-5xl font-bold text-gray-900 mb-6">
                Meet 
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-green-600"> Udbhav Patel</span>
              </h1>
              <p className="text-xl text-gray-700 mb-8 leading-relaxed">
                Visionary leader, passionate educator, and adventure enthusiast who has dedicated his life to 
                transforming education through the power of experiential learning and travel.
              </p>
              <div className="flex flex-wrap gap-4">
                <Button size="lg" className="bg-gradient-to-r from-blue-600 to-green-600">
                  Read His Story
                </Button>
                <Button variant="outline" size="lg">
                  Connect with Udbhav
                </Button>
              </div>
            </div>
            <div className="relative">
              <div className="relative h-96 rounded-2xl overflow-hidden shadow-2xl">
                {/* Placeholder for Udbhav's photo */}
                <div className="w-full h-full bg-gradient-to-br from-blue-100 to-green-100 flex items-center justify-center">
                  <Users className="w-32 h-32 text-gray-400" />
                </div>
              </div>
              {/* Floating Achievement Cards */}
              <div className="absolute -bottom-6 -left-6 bg-white rounded-xl p-4 shadow-lg">
                <div className="text-2xl font-bold text-blue-600">15+</div>
                <div className="text-xs text-gray-600">Years Leading</div>
              </div>
              <div className="absolute -top-6 -right-6 bg-white rounded-xl p-4 shadow-lg">
                <div className="text-2xl font-bold text-green-600">50K+</div>
                <div className="text-xs text-gray-600">Lives Touched</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Quote Section */}
      <section className="py-16 bg-gradient-to-r from-blue-600 to-green-600">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <Quote className="w-12 h-12 text-white/50 mx-auto mb-6" />
          <blockquote className="text-2xl md:text-3xl font-medium text-white mb-6 leading-relaxed">
            "Education is not just about what happens in the classroom. The real magic happens when students 
            step out of their comfort zones, explore new places, and discover their true potential."
          </blockquote>
          <cite className="text-blue-100 text-lg">- Udbhav Patel, Founder & CEO</cite>
        </div>
      </section>

      {/* Biography Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid lg:grid-cols-3 gap-12">
            {/* Main Story */}
            <div className="lg:col-span-2">
              <h2 className="text-4xl font-bold text-gray-900 mb-8">The Journey Begins</h2>
              
              <div className="prose prose-lg max-w-none text-gray-700 space-y-6">
                <p>
                  Udbhav Patel's journey into educational tourism began with a simple observation: traditional classroom 
                  learning, while important, often failed to ignite the spark of curiosity and wonder that comes from 
                  real-world experiences. As a young educator himself, he witnessed firsthand how students transformed 
                  when they stepped outside the confines of textbooks and into the world of experiential learning.
                </p>
                
                <p>
                  In 2009, with nothing but a vision and unwavering determination, Udbhav founded Positive7 Educational Tours. 
                  The name itself reflects his philosophy - maintaining a positive outlook while embracing the seven key 
                  elements of transformative education: Adventure, Discovery, Growth, Connection, Learning, Safety, and Joy.
                </p>
                
                <p>
                  What started as a small initiative with 50 students on a trip to Manali has now grown into one of India's 
                  most trusted educational tour companies. Under Udbhav's leadership, Positive7 has touched the lives of 
                  over 50,000 students, partnered with 500+ schools, and created countless memories that last a lifetime.
                </p>
                
                <p>
                  Udbhav's approach to educational tourism goes beyond mere sightseeing. He believes in creating immersive 
                  experiences that challenge students, build character, and foster a deep appreciation for India's rich 
                  cultural and natural heritage. His programs are meticulously designed to align with educational curricula 
                  while providing opportunities for personal growth and self-discovery.
                </p>
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-8">
              {/* Quick Facts */}
              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <h3 className="text-xl font-bold text-gray-900 mb-6">Quick Facts</h3>
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <Calendar className="w-5 h-5 text-blue-600" />
                    <div>
                      <div className="font-medium">Founded Positive7</div>
                      <div className="text-sm text-gray-600">2009</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <MapPin className="w-5 h-5 text-green-600" />
                    <div>
                      <div className="font-medium">Based in</div>
                      <div className="text-sm text-gray-600">Ahmedabad, Gujarat</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Users className="w-5 h-5 text-purple-600" />
                    <div>
                      <div className="font-medium">Students Impacted</div>
                      <div className="text-sm text-gray-600">50,000+</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Award className="w-5 h-5 text-yellow-600" />
                    <div>
                      <div className="font-medium">Awards Received</div>
                      <div className="text-sm text-gray-600">25+</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Philosophy */}
              <div className="bg-gradient-to-br from-blue-50 to-green-50 rounded-2xl p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-4">Core Philosophy</h3>
                <div className="space-y-3">
                  <div className="flex items-start gap-3">
                    <Heart className="w-5 h-5 text-red-500 mt-1" />
                    <div>
                      <div className="font-medium text-gray-900">Passion-Driven Learning</div>
                      <div className="text-sm text-gray-600">Education should ignite passion, not just transfer knowledge</div>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <Mountain className="w-5 h-5 text-blue-500 mt-1" />
                    <div>
                      <div className="font-medium text-gray-900">Challenge & Growth</div>
                      <div className="text-sm text-gray-600">Real growth happens outside comfort zones</div>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <Globe className="w-5 h-5 text-green-500 mt-1" />
                    <div>
                      <div className="font-medium text-gray-900">Global Perspective</div>
                      <div className="text-sm text-gray-600">Fostering cultural understanding and global citizenship</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Achievements & Recognition */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Recognition & Achievements</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Udbhav's dedication to educational excellence has been recognized by industry leaders and institutions
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: Award,
                title: 'Gujarat Tourism Excellence Award',
                year: '2018',
                description: 'Recognized for outstanding contribution to educational tourism in Gujarat',
                color: 'from-yellow-500 to-orange-500'
              },
              {
                icon: Star,
                title: 'Educational Innovation Award',
                year: '2020',
                description: 'For pioneering virtual learning experiences during the pandemic',
                color: 'from-blue-500 to-indigo-500'
              },
              {
                icon: Users,
                title: 'Youth Development Champion',
                year: '2021',
                description: 'Honored for significant impact on student development and growth',
                color: 'from-green-500 to-emerald-500'
              },
              {
                icon: Globe,
                title: 'Sustainable Tourism Advocate',
                year: '2022',
                description: 'Recognition for promoting eco-friendly and responsible tourism practices',
                color: 'from-teal-500 to-cyan-500'
              },
              {
                icon: BookOpen,
                title: 'Education Leadership Excellence',
                year: '2023',
                description: 'For transformative leadership in the education and tourism sector',
                color: 'from-purple-500 to-violet-500'
              },
              {
                icon: Heart,
                title: 'Community Impact Award',
                year: '2024',
                description: 'For positive impact on local communities through tourism initiatives',
                color: 'from-pink-500 to-rose-500'
              }
            ].map((achievement, index) => (
              <div key={index} className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <div className={`w-12 h-12 bg-gradient-to-r ${achievement.color} rounded-full flex items-center justify-center mb-4`}>
                  <achievement.icon className="w-6 h-6 text-white" />
                </div>
                <div className="text-sm text-gray-500 mb-2">{achievement.year}</div>
                <h3 className="text-lg font-bold text-gray-900 mb-3">{achievement.title}</h3>
                <p className="text-gray-600 text-sm">{achievement.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Vision for the Future */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-4xl font-bold text-gray-900 mb-6">Vision for the Future</h2>
              <p className="text-lg text-gray-700 mb-6 leading-relaxed">
                As Positive7 continues to grow, Udbhav remains committed to his original vision while embracing 
                new technologies and methodologies that can enhance the educational travel experience.
              </p>
              
              <div className="space-y-4">
                <div className="flex items-start gap-4">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <Target className="w-4 h-4 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Expanding Horizons</h3>
                    <p className="text-gray-600">Plans to introduce international educational programs and cultural exchange initiatives</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-4">
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <Lightbulb className="w-4 h-4 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Technology Integration</h3>
                    <p className="text-gray-600">Leveraging AI and digital platforms to create more personalized learning experiences</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-4">
                  <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <Heart className="w-4 h-4 text-purple-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Community Impact</h3>
                    <p className="text-gray-600">Strengthening partnerships with local communities to create mutual benefits</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="bg-gradient-to-br from-blue-50 to-green-50 rounded-2xl p-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-6">Get in Touch</h3>
              <p className="text-gray-700 mb-6">
                Udbhav is always excited to connect with educators, parents, and students who share his passion 
                for transformative learning experiences.
              </p>
              
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                    <Users className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <div className="font-medium">Direct Contact</div>
                    <div className="text-sm text-gray-600"><EMAIL></div>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-green-600 rounded-full flex items-center justify-center">
                    <MapPin className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <div className="font-medium">Office Location</div>
                    <div className="text-sm text-gray-600">Ahmedabad, Gujarat</div>
                  </div>
                </div>
              </div>
              
              <div className="mt-6">
                <Button className="w-full bg-gradient-to-r from-blue-600 to-green-600">
                  Schedule a Meeting
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
