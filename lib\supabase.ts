import { createClient } from '@supabase/supabase-js';
import { createClientComponentClient, createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import type { Database } from '@/types/supabase';

// Environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

// Client-side Supabase client
export const createClientSupabase = () => {
  return createClientComponentClient<Database>();
};

// Server-side Supabase client
export const createServerSupabase = () => {
  return createServerComponentClient<Database>({
    cookies,
  });
};

// Service role client (for admin operations)
export const createServiceSupabase = () => {
  return createClient<Database>(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  });
};

// Default client for general use
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey);

// Helper function to get user session
export const getSession = async () => {
  const supabase = createServerSupabase();
  const { data: { session }, error } = await supabase.auth.getSession();
  
  if (error) {
    console.error('Error getting session:', error);
    return null;
  }
  
  return session;
};

// Helper function to get current user
export const getCurrentUser = async () => {
  const session = await getSession();
  if (!session?.user) return null;
  
  const supabase = createServerSupabase();
  const { data: user, error } = await supabase
    .from('users')
    .select('*')
    .eq('id', session.user.id)
    .single();
    
  if (error) {
    console.error('Error getting user:', error);
    return null;
  }
  
  return user;
};

// Helper function to check if user is admin
export const isAdmin = async () => {
  const user = await getCurrentUser();
  return user?.role === 'admin';
};

// Helper function to verify user session and role
export const verifySession = async (requiredRole?: 'admin' | 'customer') => {
  const session = await getSession();
  if (!session?.user) return null;
  
  if (requiredRole) {
    const user = await getCurrentUser();
    if (!user || user.role !== requiredRole) return null;
  }
  
  return session;
};

// Storage helpers
export const uploadFile = async (
  bucket: string,
  path: string,
  file: File,
  options?: { upsert?: boolean }
) => {
  const supabase = createClientSupabase();
  
  const { data, error } = await supabase.storage
    .from(bucket)
    .upload(path, file, options);
    
  if (error) {
    console.error('Error uploading file:', error);
    return { data: null, error };
  }
  
  // Get public URL
  const { data: { publicUrl } } = supabase.storage
    .from(bucket)
    .getPublicUrl(path);
    
  return { data: { ...data, publicUrl }, error: null };
};

export const deleteFile = async (bucket: string, path: string) => {
  const supabase = createClientSupabase();
  
  const { error } = await supabase.storage
    .from(bucket)
    .remove([path]);
    
  if (error) {
    console.error('Error deleting file:', error);
    return { error };
  }
  
  return { error: null };
};

// Database helpers
export const withErrorHandling = async <T>(
  operation: () => Promise<{ data: T | null; error: any }>
): Promise<{ data: T | null; error: string | null }> => {
  try {
    const { data, error } = await operation();
    
    if (error) {
      console.error('Database error:', error);
      return { data: null, error: error.message || 'Database operation failed' };
    }
    
    return { data, error: null };
  } catch (err) {
    console.error('Unexpected error:', err);
    return { data: null, error: 'An unexpected error occurred' };
  }
};

// Real-time subscription helpers
export const subscribeToTable = (
  table: string,
  callback: (payload: any) => void,
  filter?: string
) => {
  const supabase = createClientSupabase();
  
  let subscription = supabase
    .channel(`${table}_changes`)
    .on('postgres_changes', 
      { 
        event: '*', 
        schema: 'public', 
        table,
        filter 
      }, 
      callback
    )
    .subscribe();
    
  return subscription;
};

export const unsubscribe = (subscription: any) => {
  if (subscription) {
    subscription.unsubscribe();
  }
};
