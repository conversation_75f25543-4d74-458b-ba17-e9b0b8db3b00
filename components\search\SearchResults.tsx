'use client'

import { useState, useEffect, useMemo } from 'react'
import { useSearchPara<PERSON>, useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import Image from 'next/image'
import Link from 'next/link'
import {
  Search,
  Filter,
  SlidersHorizontal,
  MapPin,
  Calendar,
  Users,
  Star,
  Clock,
  Grid3X3,
  List,
  ArrowUpDown,
  X
} from 'lucide-react'
import <PERSON><PERSON> from '@/components/ui/Button'

interface Trip {
  id: string
  title: string
  destination: string
  duration: string
  price: number
  rating: number
  reviewCount: number
  image: string
  category: string
  difficulty: string
  highlights: string[]
  dates: Array<{
    start: string
    end: string
    available: boolean
  }>
  description: string
  tags: string[]
}

// Sample trips data based on scraped content
const SAMPLE_TRIPS: Trip[] = [
  {
    id: 'manali-students-tour',
    title: 'Blissful Manali Students Tour',
    destination: 'Manali, Himachal Pradesh',
    duration: '9 Days 8 Nights',
    price: 27500,
    rating: 4.8,
    reviewCount: 156,
    image: 'https://positive7.in/wp-content/uploads/2025/01/gettyimages-1134041601-612x612-1.jpg',
    category: 'Adventure',
    difficulty: 'Moderate',
    highlights: ['Snow Activities', 'River Rafting', 'Trekking', 'Bonfire Night'],
    dates: [
      { start: '2024-02-15', end: '2024-02-23', available: true },
      { start: '2024-03-11', end: '2024-03-19', available: true }
    ],
    description: 'Experience the beauty of Manali with snow activities, trekking, and adventure sports.',
    tags: ['snow', 'adventure', 'trekking', 'mountains', 'himachal']
  },
  {
    id: 'rishikesh-adventure',
    title: 'Rishikesh Adventure Tour',
    destination: 'Rishikesh, Uttarakhand',
    duration: '7 Days 6 Nights',
    price: 22500,
    rating: 4.7,
    reviewCount: 142,
    image: 'https://positive7.in/wp-content/uploads/2022/09/dusk-time-rishikesh-holy-town-travel-destination-india-1024x684.jpg',
    category: 'Adventure',
    difficulty: 'Moderate',
    highlights: ['River Rafting', 'Bungee Jumping', 'Yoga', 'Temple Visits'],
    dates: [
      { start: '2024-02-20', end: '2024-02-26', available: true },
      { start: '2024-03-15', end: '2024-03-21', available: true }
    ],
    description: 'Adventure capital of India with river rafting, bungee jumping, and spiritual experiences.',
    tags: ['rafting', 'adventure', 'yoga', 'spiritual', 'uttarakhand']
  },
  {
    id: 'tirthan-valley',
    title: 'Tirthan Valley & Jibhi',
    destination: 'Tirthan Valley, Himachal Pradesh',
    duration: '9 Days 8 Nights',
    price: 29500,
    rating: 4.9,
    reviewCount: 98,
    image: 'https://positive7.in/wp-content/uploads/2024/11/TIRTHAN-VALLEY-JIBHI-1024x697.webp',
    category: 'Nature',
    difficulty: 'Easy',
    highlights: ['Nature Walks', 'Trout Fishing', 'Village Experience', 'Photography'],
    dates: [
      { start: '2024-03-01', end: '2024-03-09', available: true },
      { start: '2024-04-05', end: '2024-04-13', available: false }
    ],
    description: 'Pristine valley experience with nature walks, fishing, and authentic village life.',
    tags: ['nature', 'valley', 'fishing', 'village', 'photography']
  },
  {
    id: 'dharamshala-cultural',
    title: 'Dharamshala Cultural Tour',
    destination: 'Dharamshala, Himachal Pradesh',
    duration: '10 Days 9 Nights',
    price: 32500,
    rating: 4.6,
    reviewCount: 167,
    image: 'https://positive7.in/wp-content/uploads/2024/11/AMRITSAR-DHARAMSHALA-MCLEODGANJ-TRIUND-DALHOUSIE.webp',
    category: 'Cultural',
    difficulty: 'Easy',
    highlights: ['Dalai Lama Temple', 'Tibetan Culture', 'McLeod Ganj', 'Triund Trek'],
    dates: [
      { start: '2024-02-25', end: '2024-03-05', available: true },
      { start: '2024-03-20', end: '2024-03-29', available: true }
    ],
    description: 'Explore Tibetan culture, visit monasteries, and experience the spiritual side of Himalayas.',
    tags: ['cultural', 'tibetan', 'spiritual', 'monastery', 'mcleodganj']
  },
  {
    id: 'brigu-lake-trek',
    title: 'Brigu Lake Trek',
    destination: 'Manali, Himachal Pradesh',
    duration: '9 Days 8 Nights',
    price: 31500,
    rating: 4.8,
    reviewCount: 89,
    image: 'https://positive7.in/wp-content/uploads/2024/11/BRIGU-LAKE2.webp',
    category: 'Adventure',
    difficulty: 'Challenging',
    highlights: ['High Altitude Trek', 'Alpine Lake', 'Mountain Views', 'Camping'],
    dates: [
      { start: '2024-04-15', end: '2024-04-23', available: true },
      { start: '2024-05-10', end: '2024-05-18', available: true }
    ],
    description: 'Challenging high-altitude trek to the pristine Brigu Lake with stunning mountain views.',
    tags: ['trek', 'high-altitude', 'lake', 'camping', 'challenging']
  }
]

const CATEGORIES = ['All', 'Adventure', 'Cultural', 'Nature', 'Spiritual']
const DIFFICULTIES = ['All', 'Easy', 'Moderate', 'Challenging']
const PRICE_RANGES = [
  { label: 'All Prices', min: 0, max: Infinity },
  { label: 'Under ₹25,000', min: 0, max: 25000 },
  { label: '₹25,000 - ₹30,000', min: 25000, max: 30000 },
  { label: 'Above ₹30,000', min: 30000, max: Infinity }
]

const SORT_OPTIONS = [
  { value: 'relevance', label: 'Most Relevant' },
  { value: 'price-low', label: 'Price: Low to High' },
  { value: 'price-high', label: 'Price: High to Low' },
  { value: 'rating', label: 'Highest Rated' },
  { value: 'duration', label: 'Duration' }
]

export default function SearchResults() {
  const searchParams = useSearchParams()
  const router = useRouter()

  const [searchQuery, setSearchQuery] = useState(searchParams.get('q') || '')
  const [selectedCategory, setSelectedCategory] = useState('All')
  const [selectedDifficulty, setSelectedDifficulty] = useState('All')
  const [selectedPriceRange, setSelectedPriceRange] = useState(0)
  const [sortBy, setSortBy] = useState('relevance')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [showFilters, setShowFilters] = useState(false)

  // Filter and sort trips
  const filteredTrips = useMemo(() => {
    let filtered = SAMPLE_TRIPS

    // Search filter
    if (searchQuery) {
      filtered = filtered.filter(trip =>
        trip.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        trip.destination.toLowerCase().includes(searchQuery.toLowerCase()) ||
        trip.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        trip.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase())) ||
        trip.highlights.some(highlight => highlight.toLowerCase().includes(searchQuery.toLowerCase()))
      )
    }

    // Category filter
    if (selectedCategory !== 'All') {
      filtered = filtered.filter(trip => trip.category === selectedCategory)
    }

    // Difficulty filter
    if (selectedDifficulty !== 'All') {
      filtered = filtered.filter(trip => trip.difficulty === selectedDifficulty)
    }

    // Price range filter
    const priceRange = PRICE_RANGES[selectedPriceRange]
    filtered = filtered.filter(trip => trip.price >= priceRange.min && trip.price <= priceRange.max)

    // Sort
    switch (sortBy) {
      case 'price-low':
        filtered.sort((a, b) => a.price - b.price)
        break
      case 'price-high':
        filtered.sort((a, b) => b.price - a.price)
        break
      case 'rating':
        filtered.sort((a, b) => b.rating - a.rating)
        break
      case 'duration':
        filtered.sort((a, b) => parseInt(a.duration) - parseInt(b.duration))
        break
      default:
        // Keep original order for relevance
        break
    }

    return filtered
  }, [searchQuery, selectedCategory, selectedDifficulty, selectedPriceRange, sortBy])

  const handleSearch = (query: string) => {
    setSearchQuery(query)
    const params = new URLSearchParams(searchParams.toString())
    if (query) {
      params.set('q', query)
    } else {
      params.delete('q')
    }
    router.push(`/search?${params.toString()}`)
  }

  const clearFilters = () => {
    setSelectedCategory('All')
    setSelectedDifficulty('All')
    setSelectedPriceRange(0)
    setSortBy('relevance')
  }

  const hasActiveFilters = selectedCategory !== 'All' || selectedDifficulty !== 'All' || selectedPriceRange !== 0

  return (
    <div className="space-y-8">
      {/* Search Header */}
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          {searchQuery ? `Search Results for "${searchQuery}"` : 'Explore Our Tours'}
        </h1>
        <p className="text-gray-600 mb-8">
          Discover amazing educational tours and adventures across India
        </p>

        {/* Search Bar */}
        <div className="max-w-2xl mx-auto relative">
          <Search className="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => handleSearch(e.target.value)}
            placeholder="Search destinations, activities, or trip types..."
            className="w-full pl-12 pr-4 py-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg"
          />
        </div>
      </div>

      {/* Filters and Controls */}
      <div className="flex flex-col lg:flex-row gap-6">
        {/* Sidebar Filters */}
        <div className={`lg:w-80 ${showFilters ? 'block' : 'hidden lg:block'}`}>
          <div className="bg-white rounded-2xl p-6 shadow-lg sticky top-24">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Filters</h3>
              {hasActiveFilters && (
                <button
                  onClick={clearFilters}
                  className="text-sm text-blue-600 hover:text-blue-700 flex items-center gap-1"
                >
                  <X className="w-4 h-4" />
                  Clear All
                </button>
              )}
            </div>

            <div className="space-y-6">
              {/* Category Filter */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Category</h4>
                <div className="space-y-2">
                  {CATEGORIES.map((category) => (
                    <label key={category} className="flex items-center">
                      <input
                        type="radio"
                        name="category"
                        value={category}
                        checked={selectedCategory === category}
                        onChange={(e) => setSelectedCategory(e.target.value)}
                        className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                      />
                      <span className="ml-3 text-gray-700">{category}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Difficulty Filter */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Difficulty</h4>
                <div className="space-y-2">
                  {DIFFICULTIES.map((difficulty) => (
                    <label key={difficulty} className="flex items-center">
                      <input
                        type="radio"
                        name="difficulty"
                        value={difficulty}
                        checked={selectedDifficulty === difficulty}
                        onChange={(e) => setSelectedDifficulty(e.target.value)}
                        className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                      />
                      <span className="ml-3 text-gray-700">{difficulty}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Price Range Filter */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Price Range</h4>
                <div className="space-y-2">
                  {PRICE_RANGES.map((range, index) => (
                    <label key={index} className="flex items-center">
                      <input
                        type="radio"
                        name="priceRange"
                        value={index}
                        checked={selectedPriceRange === index}
                        onChange={(e) => setSelectedPriceRange(parseInt(e.target.value))}
                        className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                      />
                      <span className="ml-3 text-gray-700">{range.label}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1">
          {/* Results Header */}
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mb-6">
            <div className="flex items-center gap-4">
              <p className="text-gray-600">
                {filteredTrips.length} trip{filteredTrips.length !== 1 ? 's' : ''} found
              </p>

              {/* Mobile Filter Toggle */}
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="lg:hidden flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
              >
                <SlidersHorizontal className="w-4 h-4" />
                Filters
              </button>
            </div>

            <div className="flex items-center gap-4">
              {/* Sort Dropdown */}
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {SORT_OPTIONS.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>

              {/* View Mode Toggle */}
              <div className="flex bg-gray-100 rounded-lg p-1">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded ${viewMode === 'grid' ? 'bg-white shadow-sm' : ''}`}
                >
                  <Grid3X3 className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded ${viewMode === 'list' ? 'bg-white shadow-sm' : ''}`}
                >
                  <List className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>

          {/* Results Grid/List */}
          {filteredTrips.length > 0 ? (
            <div className={`
              ${viewMode === 'grid'
                ? 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6'
                : 'space-y-6'
              }
            `}>
              {filteredTrips.map((trip, index) => (
                <TripCard
                  key={trip.id}
                  trip={trip}
                  viewMode={viewMode}
                  index={index}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <Search className="w-16 h-16 mx-auto" />
              </div>
              <h3 className="text-xl font-medium text-gray-900 mb-2">No trips found</h3>
              <p className="text-gray-600 mb-6">
                Try adjusting your search criteria or filters
              </p>
              <Button onClick={clearFilters} variant="outline">
                Clear Filters
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

// Trip Card Component
function TripCard({ trip, viewMode, index }: { trip: Trip; viewMode: 'grid' | 'list'; index: number }) {
  const availableDates = trip.dates.filter(date => date.available)

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1 }}
      className="group"
    >
      <Link href={`/trips/${trip.id}`}>
        <div className={`
          bg-white rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-shadow duration-300 border border-gray-200
          ${viewMode === 'list' ? 'flex gap-6' : ''}
        `}>
          {/* Trip Image */}
          <div className={`relative overflow-hidden ${
            viewMode === 'list' ? 'w-80 h-48 flex-shrink-0' : 'h-48'
          }`}>
            <Image
              src={trip.image}
              alt={trip.title}
              fill
              className="object-cover transition-transform duration-300 group-hover:scale-105"
            />
            <div className="absolute top-3 left-3 flex gap-2">
              <span className="px-2 py-1 bg-blue-600 text-white text-xs font-medium rounded-full">
                {trip.category}
              </span>
              <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                trip.difficulty === 'Easy' ? 'bg-green-100 text-green-800' :
                trip.difficulty === 'Moderate' ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'
              }`}>
                {trip.difficulty}
              </span>
            </div>
            <div className="absolute top-3 right-3">
              <div className="flex items-center gap-1 bg-white/90 backdrop-blur-sm px-2 py-1 rounded-full">
                <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                <span className="text-xs font-medium">{trip.rating}</span>
              </div>
            </div>
          </div>

          {/* Trip Content */}
          <div className={`p-6 ${viewMode === 'list' ? 'flex-1' : ''}`}>
            <div className="flex items-start justify-between mb-3">
              <div className="flex-1">
                <h3 className="text-xl font-bold text-gray-900 mb-1 group-hover:text-blue-600 transition-colors">
                  {trip.title}
                </h3>
                <div className="flex items-center gap-1 text-gray-600 mb-2">
                  <MapPin className="w-4 h-4" />
                  <span className="text-sm">{trip.destination}</span>
                </div>
              </div>
            </div>

            <p className="text-gray-600 text-sm mb-4 line-clamp-2">
              {trip.description}
            </p>

            {/* Trip Details */}
            <div className="flex items-center gap-4 mb-4 text-sm text-gray-600">
              <div className="flex items-center gap-1">
                <Clock className="w-4 h-4" />
                <span>{trip.duration}</span>
              </div>
              <div className="flex items-center gap-1">
                <Users className="w-4 h-4" />
                <span>Group Tour</span>
              </div>
              <div className="flex items-center gap-1">
                <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                <span>{trip.rating} ({trip.reviewCount})</span>
              </div>
            </div>

            {/* Highlights */}
            <div className="mb-4">
              <div className="flex flex-wrap gap-1">
                {trip.highlights.slice(0, 3).map((highlight, idx) => (
                  <span
                    key={idx}
                    className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full"
                  >
                    {highlight}
                  </span>
                ))}
                {trip.highlights.length > 3 && (
                  <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                    +{trip.highlights.length - 3} more
                  </span>
                )}
              </div>
            </div>

            {/* Availability */}
            <div className="mb-4">
              <div className="text-sm text-gray-600 mb-1">Next Available:</div>
              {availableDates.length > 0 ? (
                <div className="flex items-center gap-1 text-sm">
                  <Calendar className="w-4 h-4 text-green-600" />
                  <span className="text-green-600 font-medium">
                    {new Date(availableDates[0].start).toLocaleDateString('en-IN', {
                      day: 'numeric',
                      month: 'short'
                    })}
                  </span>
                  {availableDates.length > 1 && (
                    <span className="text-gray-500">
                      (+{availableDates.length - 1} more dates)
                    </span>
                  )}
                </div>
              ) : (
                <span className="text-red-600 text-sm">No dates available</span>
              )}
            </div>

            {/* Price and CTA */}
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold text-gray-900">
                  ₹{trip.price.toLocaleString()}
                </div>
                <div className="text-sm text-gray-600">per person</div>
              </div>
              <Button
                size="sm"
                className="bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700"
              >
                View Details
              </Button>
            </div>
          </div>
        </div>
      </Link>
    </motion.div>
  )
}
