# Positive7 Tourism Website - Backend Setup

This document outlines the complete backend setup for the Positive7 Tourism Website, including database schema, authentication, API routes, and integrations.

## 🗄️ Database Schema (Task B1)

### Tables Created
- **users** - Extended user profiles with role-based access
- **trips** - Trip management with detailed information
- **trip_images** - Image gallery for trips
- **bookings** - Booking system with participant details
- **testimonials** - Customer reviews and ratings
- **blog_posts** - Content management for blog
- **inquiries** - Contact form and inquiry management
- **newsletter_subscriptions** - Email subscription management

### Key Features
- UUID primary keys for security
- Row Level Security (RLS) policies
- Proper foreign key relationships
- Optimized indexes for performance
- JSONB fields for flexible data storage

### Migration Files
- `supabase/migrations/001_initial_schema.sql` - Database schema
- `supabase/migrations/002_rls_policies.sql` - Security policies

## 🔐 Authentication System (Task B2)

### Features Implemented
- User registration and login
- Email verification
- Password reset functionality
- Social login support (Google, GitHub)
- Role-based access control (Customer, Admin)
- Profile management with image upload
- Session management with refresh tokens

### Files Created
- `lib/auth.ts` - Authentication utilities
- `contexts/AuthContext.tsx` - React context for auth state
- `middleware.ts` - Route protection middleware

### Security Features
- Protected routes with middleware
- Admin-only access controls
- Secure session handling
- Email verification required

## 🚀 Trip Management API (Task B3)

### Endpoints Created

#### `/api/trips`
- **GET** - List trips with filtering and pagination
- **POST** - Create new trip (Admin only)

#### `/api/trips/[id]`
- **GET** - Get trip details by ID or slug
- **PUT** - Update trip (Admin only)
- **DELETE** - Delete trip (Admin only)

#### `/api/trips/[id]/images`
- **GET** - Get trip images
- **POST** - Upload trip images (Admin only)
- **PUT** - Update image order and properties

### Features
- Advanced filtering (destination, difficulty, price, duration)
- Search functionality
- Image upload to Supabase Storage
- Slug-based URLs for SEO
- Availability checking
- Participant limit validation

## 💳 Booking System API (Task B4)

### Endpoints Created

#### `/api/bookings`
- **GET** - List bookings with filtering
- **POST** - Create new booking

#### `/api/bookings/[id]`
- **GET** - Get booking details
- **PUT** - Update booking
- **DELETE** - Cancel booking

#### `/api/payments/create-intent`
- **POST** - Create payment intent

### Features
- Booking reference generation
- Participant management
- Payment integration setup (Razorpay/Stripe)
- Booking status tracking
- Conflict prevention
- Email notifications (TODO)

## 📝 Content Management API (Task B5)

### Endpoints Created

#### `/api/testimonials`
- **GET** - List testimonials with filtering
- **POST** - Submit testimonial

#### `/api/blog`
- **GET** - List blog posts
- **POST** - Create blog post (Admin only)

#### `/api/inquiries`
- **GET** - List inquiries (Admin only)
- **POST** - Submit inquiry

#### `/api/newsletter`
- **GET** - List subscriptions (Admin only)
- **POST** - Subscribe to newsletter
- **DELETE** - Unsubscribe from newsletter

### Features
- Content moderation for testimonials
- SEO-friendly blog posts
- Contact form handling
- Newsletter management
- Admin dashboard APIs

## 🔧 Configuration

### Environment Variables
Copy `.env.example` to `.env.local` and configure:

```bash
# Supabase
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_key

# Payment Gateways
RAZORPAY_KEY_ID=your_razorpay_key
STRIPE_SECRET_KEY=your_stripe_key

# Email Service
SENDGRID_API_KEY=your_sendgrid_key
```

### Supabase Setup
1. Create a new Supabase project
2. Run the migration files in order
3. Configure storage buckets:
   - `trip-images` - For trip gallery images
   - `profiles` - For user profile images
4. Set up authentication providers in Supabase dashboard

## 📊 TypeScript Types

### Database Types
- `types/database.ts` - Application-specific types
- `types/supabase.ts` - Generated Supabase types

### Key Interfaces
- `User` - User profile with role
- `Trip` - Trip details with itinerary
- `Booking` - Booking with participants
- `Testimonial` - Customer review
- `BlogPost` - Blog content
- `Inquiry` - Contact form submission

## 🛡️ Security Features

### Row Level Security (RLS)
- Users can only access their own data
- Admins have full access
- Public content is properly filtered
- Secure file uploads

### API Security
- Authentication required for protected routes
- Role-based authorization
- Input validation and sanitization
- Error handling without data leakage

## 🚀 Next Steps

### Integration Tasks
1. **Payment Gateway Integration**
   - Complete Razorpay/Stripe setup
   - Implement webhook handlers
   - Add payment confirmation flow

2. **Email System**
   - Set up SendGrid/Resend
   - Create email templates
   - Implement notification system

3. **File Upload**
   - Configure Cloudinary/AWS S3
   - Implement image optimization
   - Add file validation

4. **External APIs**
   - Weather API integration
   - Google Maps integration
   - Social media feeds

### Performance Optimizations
- Database query optimization
- Caching strategies
- Image optimization
- API rate limiting

### Monitoring & Analytics
- Error tracking (Sentry)
- Performance monitoring
- User analytics
- API usage tracking

## 📚 API Documentation

All APIs follow RESTful conventions with:
- Consistent error responses
- Pagination for list endpoints
- Filtering and search capabilities
- Proper HTTP status codes
- JSON request/response format

### Error Response Format
```json
{
  "error": "Error message",
  "details": "Additional details if applicable"
}
```

### Success Response Format
```json
{
  "data": "Response data",
  "message": "Success message",
  "pagination": "Pagination info for lists"
}
```

## 🧪 Testing

### Recommended Testing Strategy
1. Unit tests for utility functions
2. Integration tests for API routes
3. E2E tests for critical user flows
4. Database migration testing
5. Security testing for RLS policies

### Test Files Structure
```
tests/
├── unit/
├── integration/
├── e2e/
└── fixtures/
```

This backend setup provides a solid foundation for the Positive7 Tourism Website with scalability, security, and maintainability in mind.
