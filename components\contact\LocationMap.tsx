'use client'

import { motion } from 'framer-motion'
import { 
  MapPin, 
  Navigation, 
  Car, 
  Train, 
  Plane,
  ExternalLink
} from 'lucide-react'
import Button from '@/components/ui/Button'

const LOCATION_INFO = {
  address: '904, Shivalik Highstreet, B/S ITC Narmada Hotel, Vastrapur, Ahmedabad, Gujarat 380015',
  coordinates: {
    lat: 23.0225,
    lng: 72.5714
  },
  landmarks: [
    'Behind ITC Narmada Hotel',
    'Near Vastrapur Lake',
    'Close to Gujarat University',
    'Opposite Himalaya Mall'
  ]
}

const TRANSPORTATION = [
  {
    icon: Car,
    title: 'By Car',
    description: 'Free parking available in the building',
    details: '15 minutes from Ahmedabad Railway Station'
  },
  {
    icon: Train,
    title: 'By Metro',
    description: 'Vastrapur Metro Station',
    details: '5 minutes walk from the station'
  },
  {
    icon: Plane,
    title: 'From Airport',
    description: 'Sardar <PERSON> Patel Airport',
    details: '20 minutes drive via SG Highway'
  }
]

export function LocationMap() {
  const openInMaps = () => {
    const url = `https://maps.google.com/?q=${LOCATION_INFO.coordinates.lat},${LOCATION_INFO.coordinates.lng}`
    window.open(url, '_blank')
  }

  const getDirections = () => {
    const url = `https://maps.google.com/dir/?api=1&destination=${LOCATION_INFO.coordinates.lat},${LOCATION_INFO.coordinates.lng}`
    window.open(url, '_blank')
  }

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">Visit Our Office</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Located in the heart of Ahmedabad, our office is easily accessible and equipped with 
            modern facilities to discuss your educational travel needs.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12">
          {/* Map Placeholder */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            className="relative"
          >
            <div className="bg-white rounded-2xl p-8 shadow-lg">
              {/* Interactive Map Placeholder */}
              <div className="relative h-80 bg-gradient-to-br from-blue-100 to-green-100 rounded-xl overflow-hidden mb-6">
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center">
                    <MapPin className="w-16 h-16 text-blue-600 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Positive7 Office</h3>
                    <p className="text-gray-600 text-sm">Vastrapur, Ahmedabad</p>
                  </div>
                </div>
                
                {/* In a real implementation, you would integrate with Google Maps or another mapping service */}
                <div className="absolute top-4 right-4">
                  <Button
                    size="sm"
                    variant="outline"
                    className="bg-white/90 backdrop-blur-sm"
                    onClick={openInMaps}
                  >
                    <ExternalLink className="w-4 h-4 mr-2" />
                    Open in Maps
                  </Button>
                </div>
              </div>

              {/* Address and Actions */}
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Address</h4>
                  <p className="text-gray-700 text-sm leading-relaxed">{LOCATION_INFO.address}</p>
                </div>

                <div className="flex flex-col sm:flex-row gap-3">
                  <Button
                    onClick={getDirections}
                    className="flex-1 bg-gradient-to-r from-blue-600 to-green-600"
                  >
                    <Navigation className="w-4 h-4 mr-2" />
                    Get Directions
                  </Button>
                  <Button
                    variant="outline"
                    onClick={openInMaps}
                    className="flex-1"
                  >
                    <MapPin className="w-4 h-4 mr-2" />
                    View on Map
                  </Button>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Location Details */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            {/* Landmarks */}
            <div className="bg-white rounded-2xl p-6 shadow-lg">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Nearby Landmarks</h3>
              <ul className="space-y-3">
                {LOCATION_INFO.landmarks.map((landmark, index) => (
                  <li key={index} className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-blue-600 rounded-full" />
                    <span className="text-gray-700">{landmark}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Transportation */}
            <div className="bg-white rounded-2xl p-6 shadow-lg">
              <h3 className="text-xl font-semibold text-gray-900 mb-6">How to Reach</h3>
              <div className="space-y-6">
                {TRANSPORTATION.map((transport, index) => (
                  <div key={index} className="flex items-start gap-4">
                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                      <transport.icon className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 mb-1">{transport.title}</h4>
                      <p className="text-gray-700 text-sm mb-1">{transport.description}</p>
                      <p className="text-gray-500 text-xs">{transport.details}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Office Features */}
            <div className="bg-gradient-to-br from-blue-50 to-green-50 rounded-2xl p-6 border border-gray-200">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Office Facilities</h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-2">
                    <Car className="w-4 h-4 text-white" />
                  </div>
                  <div className="text-sm font-medium text-gray-900">Free Parking</div>
                </div>
                <div className="text-center">
                  <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-2">
                    <MapPin className="w-4 h-4 text-white" />
                  </div>
                  <div className="text-sm font-medium text-gray-900">Easy Access</div>
                </div>
                <div className="text-center">
                  <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-2">
                    <Navigation className="w-4 h-4 text-white" />
                  </div>
                  <div className="text-sm font-medium text-gray-900">Metro Nearby</div>
                </div>
                <div className="text-center">
                  <div className="w-8 h-8 bg-orange-600 rounded-full flex items-center justify-center mx-auto mb-2">
                    <Plane className="w-4 h-4 text-white" />
                  </div>
                  <div className="text-sm font-medium text-gray-900">Airport Access</div>
                </div>
              </div>
            </div>

            {/* Visit Note */}
            <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-4">
              <h4 className="font-medium text-yellow-900 mb-2">Planning to Visit?</h4>
              <p className="text-yellow-800 text-sm mb-3">
                We recommend scheduling an appointment to ensure our team is available to give you 
                personalized attention and discuss your educational travel requirements in detail.
              </p>
              <Button
                size="sm"
                variant="outline"
                className="border-yellow-300 text-yellow-700 hover:bg-yellow-100"
              >
                Schedule Appointment
              </Button>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
