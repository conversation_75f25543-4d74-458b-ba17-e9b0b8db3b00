/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/about/page";
exports.ids = ["app/about/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fabout%2Fpage&page=%2Fabout%2Fpage&appPaths=%2Fabout%2Fpage&pagePath=private-next-app-dir%2Fabout%2Fpage.tsx&appDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fabout%2Fpage&page=%2Fabout%2Fpage&appPaths=%2Fabout%2Fpage&pagePath=private-next-app-dir%2Fabout%2Fpage.tsx&appDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'about',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/about/page.tsx */ \"(rsc)/./app/about/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/about/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/about/page\",\n        pathname: \"/about\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fabout%2Fpage&page=%2Fabout%2Fpage&appPaths=%2Fabout%2Fpage&pagePath=private-next-app-dir%2Fabout%2Fpage.tsx&appDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Ccomponents%5Cabout%5CStatsSection.tsx&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Ccomponents%5Cabout%5CTeamSection.tsx&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Ccomponents%5Cabout%5CTimelineSection.tsx&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Ccomponents%5Cabout%5CValuesSection.tsx&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Ccomponents%5Cui%5CButton.tsx&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cimage-component.js&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Ccomponents%5Cabout%5CStatsSection.tsx&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Ccomponents%5Cabout%5CTeamSection.tsx&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Ccomponents%5Cabout%5CTimelineSection.tsx&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Ccomponents%5Cabout%5CValuesSection.tsx&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Ccomponents%5Cui%5CButton.tsx&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cimage-component.js&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/about/StatsSection.tsx */ \"(ssr)/./components/about/StatsSection.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/about/TeamSection.tsx */ \"(ssr)/./components/about/TeamSection.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/about/TimelineSection.tsx */ \"(ssr)/./components/about/TimelineSection.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/about/ValuesSection.tsx */ \"(ssr)/./components/about/ValuesSection.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/Button.tsx */ \"(ssr)/./components/ui/Button.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(ssr)/./node_modules/next/dist/client/image-component.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Ccomponents%5Cabout%5CStatsSection.tsx&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Ccomponents%5Cabout%5CTeamSection.tsx&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Ccomponents%5Cabout%5CTimelineSection.tsx&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Ccomponents%5Cabout%5CValuesSection.tsx&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Ccomponents%5Cui%5CButton.tsx&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cimage-component.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Ccontexts%5CAuthContext.tsx&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Poppins%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%2C%22800%22%5D%2C%22variable%22%3A%22--font-poppins%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22poppins%22%7D&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp%5Cglobals.css&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Ccontexts%5CAuthContext.tsx&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Poppins%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%2C%22800%22%5D%2C%22variable%22%3A%22--font-poppins%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22poppins%22%7D&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp%5Cglobals.css&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./contexts/AuthContext.tsx */ \"(ssr)/./contexts/AuthContext.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDcGVlYnMlNUNEb2N1bWVudHMlNUNwcm9qZWN0cyU1Q3A3LWNvbXByZWhlbnNpdmUlNUNjb250ZXh0cyU1Q0F1dGhDb250ZXh0LnRzeCZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q3BlZWJzJTVDRG9jdW1lbnRzJTVDcHJvamVjdHMlNUNwNy1jb21wcmVoZW5zaXZlJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2ZvbnQlNUNnb29nbGUlNUN0YXJnZXQuY3NzJTNGJTdCJTIycGF0aCUyMiUzQSUyMmFwcCU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpbXBvcnQlMjIlM0ElMjJJbnRlciUyMiUyQyUyMmFyZ3VtZW50cyUyMiUzQSU1QiU3QiUyMnN1YnNldHMlMjIlM0ElNUIlMjJsYXRpbiUyMiU1RCUyQyUyMnZhcmlhYmxlJTIyJTNBJTIyLS1mb250LWludGVyJTIyJTJDJTIyZGlzcGxheSUyMiUzQSUyMnN3YXAlMjIlN0QlNUQlMkMlMjJ2YXJpYWJsZU5hbWUlMjIlM0ElMjJpbnRlciUyMiU3RCZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q3BlZWJzJTVDRG9jdW1lbnRzJTVDcHJvamVjdHMlNUNwNy1jb21wcmVoZW5zaXZlJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2ZvbnQlNUNnb29nbGUlNUN0YXJnZXQuY3NzJTNGJTdCJTIycGF0aCUyMiUzQSUyMmFwcCU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpbXBvcnQlMjIlM0ElMjJQb3BwaW5zJTIyJTJDJTIyYXJndW1lbnRzJTIyJTNBJTVCJTdCJTIyc3Vic2V0cyUyMiUzQSU1QiUyMmxhdGluJTIyJTVEJTJDJTIyd2VpZ2h0JTIyJTNBJTVCJTIyMzAwJTIyJTJDJTIyNDAwJTIyJTJDJTIyNTAwJTIyJTJDJTIyNjAwJTIyJTJDJTIyNzAwJTIyJTJDJTIyODAwJTIyJTVEJTJDJTIydmFyaWFibGUlMjIlM0ElMjItLWZvbnQtcG9wcGlucyUyMiUyQyUyMmRpc3BsYXklMjIlM0ElMjJzd2FwJTIyJTdEJTVEJTJDJTIydmFyaWFibGVOYW1lJTIyJTNBJTIycG9wcGlucyUyMiU3RCZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q3BlZWJzJTVDRG9jdW1lbnRzJTVDcHJvamVjdHMlNUNwNy1jb21wcmVoZW5zaXZlJTVDYXBwJTVDZ2xvYmFscy5jc3Mmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcG9zaXRpdmU3LXRvdXJpc20td2Vic2l0ZS8/ZjU2YiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHBlZWJzXFxcXERvY3VtZW50c1xcXFxwcm9qZWN0c1xcXFxwNy1jb21wcmVoZW5zaXZlXFxcXGNvbnRleHRzXFxcXEF1dGhDb250ZXh0LnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Ccontexts%5CAuthContext.tsx&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Poppins%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%2C%22800%22%5D%2C%22variable%22%3A%22--font-poppins%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22poppins%22%7D&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp%5Cglobals.css&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./components/about/StatsSection.tsx":
/*!*******************************************!*\
  !*** ./components/about/StatsSection.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StatsSection: () => (/* binding */ StatsSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Globe_Heart_MapPin_School_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Globe,Heart,MapPin,School,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Globe_Heart_MapPin_School_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Globe,Heart,MapPin,School,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/school.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Globe_Heart_MapPin_School_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Globe,Heart,MapPin,School,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Globe_Heart_MapPin_School_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Globe,Heart,MapPin,School,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Globe_Heart_MapPin_School_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Globe,Heart,MapPin,School,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Globe_Heart_MapPin_School_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Globe,Heart,MapPin,School,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Globe_Heart_MapPin_School_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Globe,Heart,MapPin,School,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_Globe_Heart_MapPin_School_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,Globe,Heart,MapPin,School,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* __next_internal_client_entry_do_not_use__ StatsSection auto */ \n\n\n\nconst STATS = [\n    {\n        icon: _barrel_optimize_names_Award_Calendar_Globe_Heart_MapPin_School_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        value: 50000,\n        suffix: \"+\",\n        label: \"Students Impacted\",\n        description: \"Lives transformed through educational travel\",\n        color: \"from-blue-500 to-blue-600\"\n    },\n    {\n        icon: _barrel_optimize_names_Award_Calendar_Globe_Heart_MapPin_School_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        value: 500,\n        suffix: \"+\",\n        label: \"Schools Partnered\",\n        description: \"Educational institutions across India\",\n        color: \"from-green-500 to-green-600\"\n    },\n    {\n        icon: _barrel_optimize_names_Award_Calendar_Globe_Heart_MapPin_School_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        value: 100,\n        suffix: \"+\",\n        label: \"Destinations\",\n        description: \"Carefully curated learning locations\",\n        color: \"from-purple-500 to-purple-600\"\n    },\n    {\n        icon: _barrel_optimize_names_Award_Calendar_Globe_Heart_MapPin_School_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        value: 15,\n        suffix: \"+\",\n        label: \"Years of Excellence\",\n        description: \"Pioneering educational tourism since 2009\",\n        color: \"from-orange-500 to-orange-600\"\n    },\n    {\n        icon: _barrel_optimize_names_Award_Calendar_Globe_Heart_MapPin_School_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        value: 25,\n        suffix: \"+\",\n        label: \"Awards & Recognition\",\n        description: \"Industry accolades and certifications\",\n        color: \"from-yellow-500 to-yellow-600\"\n    },\n    {\n        icon: _barrel_optimize_names_Award_Calendar_Globe_Heart_MapPin_School_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        value: 4.8,\n        suffix: \"/5\",\n        label: \"Average Rating\",\n        description: \"Based on 1000+ reviews from students and parents\",\n        color: \"from-pink-500 to-pink-600\"\n    },\n    {\n        icon: _barrel_optimize_names_Award_Calendar_Globe_Heart_MapPin_School_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        value: 12,\n        suffix: \"\",\n        label: \"States Covered\",\n        description: \"Diverse destinations across India\",\n        color: \"from-teal-500 to-teal-600\"\n    },\n    {\n        icon: _barrel_optimize_names_Award_Calendar_Globe_Heart_MapPin_School_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        value: 98,\n        suffix: \"%\",\n        label: \"Satisfaction Rate\",\n        description: \"Students who would recommend us\",\n        color: \"from-red-500 to-red-600\"\n    }\n];\n// Counter animation hook\nfunction useCounter(end, duration = 2000) {\n    const [count, setCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isVisible) return;\n        let startTime;\n        let animationFrame;\n        const animate = (currentTime)=>{\n            if (!startTime) startTime = currentTime;\n            const progress = Math.min((currentTime - startTime) / duration, 1);\n            setCount(Math.floor(progress * end));\n            if (progress < 1) {\n                animationFrame = requestAnimationFrame(animate);\n            }\n        };\n        animationFrame = requestAnimationFrame(animate);\n        return ()=>cancelAnimationFrame(animationFrame);\n    }, [\n        end,\n        duration,\n        isVisible\n    ]);\n    return {\n        count,\n        setIsVisible\n    };\n}\nfunction StatCard({ stat, index }) {\n    const { count, setIsVisible } = useCounter(stat.value);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        whileInView: {\n            opacity: 1,\n            y: 0\n        },\n        viewport: {\n            once: true\n        },\n        transition: {\n            delay: index * 0.1\n        },\n        onViewportEnter: ()=>setIsVisible(true),\n        className: \"group\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 text-center h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `w-16 h-16 bg-gradient-to-r ${stat.color} rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                        className: \"w-8 h-8 text-white\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\StatsSection.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\StatsSection.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-4xl font-bold text-gray-900 mb-2\",\n                            children: [\n                                stat.value === 4.8 ? count.toFixed(1) : count.toLocaleString(),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: `text-transparent bg-clip-text bg-gradient-to-r ${stat.color}`,\n                                    children: stat.suffix\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\StatsSection.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\StatsSection.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-gray-900\",\n                            children: stat.label\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\StatsSection.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\StatsSection.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 text-sm leading-relaxed\",\n                    children: stat.description\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\StatsSection.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\StatsSection.tsx\",\n            lineNumber: 125,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\StatsSection.tsx\",\n        lineNumber: 117,\n        columnNumber: 5\n    }, this);\n}\nfunction StatsSection() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"Our Impact in Numbers\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\StatsSection.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                            children: \"These numbers represent more than statistics - they represent lives touched, minds opened, and futures shaped through the power of educational travel\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\StatsSection.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\StatsSection.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                    children: STATS.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            stat: stat,\n                            index: index\n                        }, index, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\StatsSection.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\StatsSection.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-16 bg-gradient-to-r from-blue-50 to-green-50 rounded-2xl p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                    children: \"More Than Just Numbers\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\StatsSection.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 max-w-4xl mx-auto leading-relaxed\",\n                                    children: \"Behind every statistic is a story of transformation. From the shy student who discovered confidence on a mountain trek, to the urban child who learned about rural life in a village homestay, to the group that bonded over a campfire under the stars - these experiences create memories and learning that last a lifetime.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\StatsSection.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\StatsSection.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-3 gap-8 mt-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-blue-600 mb-2\",\n                                            children: \"Zero\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\StatsSection.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-700\",\n                                            children: \"Major Safety Incidents\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\StatsSection.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600 mt-1\",\n                                            children: \"in 15+ years of operations\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\StatsSection.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\StatsSection.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-green-600 mb-2\",\n                                            children: \"100%\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\StatsSection.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-700\",\n                                            children: \"Curriculum Aligned\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\StatsSection.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600 mt-1\",\n                                            children: \"programs designed with educators\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\StatsSection.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\StatsSection.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-purple-600 mb-2\",\n                                            children: \"24/7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\StatsSection.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-700\",\n                                            children: \"Support Available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\StatsSection.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600 mt-1\",\n                                            children: \"throughout every journey\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\StatsSection.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\StatsSection.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\StatsSection.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\StatsSection.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\StatsSection.tsx\",\n            lineNumber: 149,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\StatsSection.tsx\",\n        lineNumber: 148,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/about/StatsSection.tsx\n");

/***/ }),

/***/ "(ssr)/./components/about/TeamSection.tsx":
/*!******************************************!*\
  !*** ./components/about/TeamSection.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TeamSection: () => (/* binding */ TeamSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Linkedin_Mail_MapPin_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Linkedin,Mail,MapPin,Phone,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Linkedin_Mail_MapPin_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Linkedin,Mail,MapPin,Phone,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Linkedin_Mail_MapPin_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Linkedin,Mail,MapPin,Phone,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Linkedin_Mail_MapPin_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Linkedin,Mail,MapPin,Phone,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Linkedin_Mail_MapPin_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Linkedin,Mail,MapPin,Phone,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Linkedin_Mail_MapPin_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Linkedin,Mail,MapPin,Phone,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Linkedin_Mail_MapPin_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Linkedin,Mail,MapPin,Phone,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* __next_internal_client_entry_do_not_use__ TeamSection auto */ \n\n\nconst TEAM_MEMBERS = [\n    {\n        name: \"Udbhav Patel\",\n        role: \"Founder & CEO\",\n        bio: \"Visionary leader with 15+ years in educational tourism. Passionate about transforming learning through travel experiences.\",\n        image: \"/images/team/udbhav-patel.jpg\",\n        expertise: [\n            \"Educational Leadership\",\n            \"Adventure Tourism\",\n            \"Student Development\"\n        ],\n        achievements: [\n            \"Gujarat Tourism Excellence Award\",\n            \"50,000+ students impacted\",\n            \"Pioneer in educational travel\"\n        ],\n        contact: {\n            email: \"<EMAIL>\",\n            phone: \"+91 78780 05500\",\n            linkedin: \"https://linkedin.com/in/udbhav-patel\"\n        }\n    },\n    {\n        name: \"Priya Sharma\",\n        role: \"Head of Operations\",\n        bio: \"Operations expert ensuring seamless execution of every trip with meticulous attention to safety and quality.\",\n        image: \"/images/team/priya-sharma.jpg\",\n        expertise: [\n            \"Operations Management\",\n            \"Safety Protocols\",\n            \"Quality Assurance\"\n        ],\n        achievements: [\n            \"Zero safety incidents record\",\n            \"ISO 9001 certification lead\",\n            \"500+ successful trips\"\n        ],\n        contact: {\n            email: \"<EMAIL>\",\n            phone: \"+91 78780 05501\"\n        }\n    },\n    {\n        name: \"Rajesh Kumar\",\n        role: \"Educational Program Director\",\n        bio: \"Former educator with deep understanding of curriculum alignment and experiential learning methodologies.\",\n        image: \"/images/team/rajesh-kumar.jpg\",\n        expertise: [\n            \"Curriculum Design\",\n            \"Teacher Training\",\n            \"Learning Assessment\"\n        ],\n        achievements: [\n            \"M.Ed in Educational Psychology\",\n            \"20+ years teaching experience\",\n            \"Curriculum alignment specialist\"\n        ],\n        contact: {\n            email: \"<EMAIL>\",\n            phone: \"+91 78780 05502\"\n        }\n    },\n    {\n        name: \"Anita Desai\",\n        role: \"Student Welfare Manager\",\n        bio: \"Dedicated to student safety and well-being, ensuring every child has a positive and secure experience.\",\n        image: \"/images/team/anita-desai.jpg\",\n        expertise: [\n            \"Child Psychology\",\n            \"Crisis Management\",\n            \"Parent Communication\"\n        ],\n        achievements: [\n            \"Certified Child Safety Officer\",\n            \"First Aid & CPR certified\",\n            \"98% parent satisfaction rate\"\n        ],\n        contact: {\n            email: \"<EMAIL>\",\n            phone: \"+91 78780 05503\"\n        }\n    },\n    {\n        name: \"Vikram Singh\",\n        role: \"Adventure Specialist\",\n        bio: \"Mountaineering expert and adventure guide with extensive experience in outdoor education and risk management.\",\n        image: \"/images/team/vikram-singh.jpg\",\n        expertise: [\n            \"Mountaineering\",\n            \"Risk Assessment\",\n            \"Outdoor Education\"\n        ],\n        achievements: [\n            \"Everest Base Camp expedition\",\n            \"Certified Mountain Guide\",\n            \"1000+ adventure activities led\"\n        ],\n        contact: {\n            email: \"<EMAIL>\",\n            phone: \"+91 78780 05504\"\n        }\n    },\n    {\n        name: \"Meera Patel\",\n        role: \"Cultural Programs Coordinator\",\n        bio: \"Cultural anthropologist specializing in immersive cultural experiences and community-based tourism.\",\n        image: \"/images/team/meera-patel.jpg\",\n        expertise: [\n            \"Cultural Anthropology\",\n            \"Community Relations\",\n            \"Heritage Tourism\"\n        ],\n        achievements: [\n            \"PhD in Cultural Studies\",\n            \"UNESCO heritage consultant\",\n            \"50+ cultural partnerships\"\n        ],\n        contact: {\n            email: \"<EMAIL>\",\n            phone: \"+91 78780 05505\"\n        }\n    }\n];\nfunction TeamSection() {\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 20\n        },\n        visible: {\n            opacity: 1,\n            y: 0\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"Meet Our Team\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                            children: \"Our passionate team of educators, adventure specialists, and travel experts work together to create unforgettable learning experiences for every student\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    whileInView: \"visible\",\n                    viewport: {\n                        once: true\n                    },\n                    className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: TEAM_MEMBERS.map((member, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            variants: itemVariants,\n                            className: \"group\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 h-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative w-32 h-32 mx-auto mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-full bg-gradient-to-r from-blue-100 to-green-100 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Linkedin_Mail_MapPin_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"w-16 h-16 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold text-gray-900 mb-2\",\n                                                children: member.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-600 font-medium mb-3\",\n                                                children: member.role\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 text-sm leading-relaxed\",\n                                                children: member.bio\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-semibold text-gray-900 mb-3\",\n                                                children: \"Expertise:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: member.expertise.map((skill, skillIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-3 py-1 bg-blue-100 text-blue-700 text-xs rounded-full\",\n                                                        children: skill\n                                                    }, skillIndex, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-semibold text-gray-900 mb-3 flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Linkedin_Mail_MapPin_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Key Achievements:\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-1\",\n                                                children: member.achievements.map((achievement, achievementIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"text-xs text-gray-600 flex items-start gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-1 h-1 bg-green-500 rounded-full mt-2 flex-shrink-0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                                lineNumber: 201,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: achievement\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                                lineNumber: 202,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, achievementIndex, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"pt-4 border-t border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: `mailto:${member.contact.email}`,\n                                                    className: \"p-2 bg-gray-100 rounded-full hover:bg-blue-100 hover:text-blue-600 transition-colors\",\n                                                    title: \"Email\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Linkedin_Mail_MapPin_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: `tel:${member.contact.phone}`,\n                                                    className: \"p-2 bg-gray-100 rounded-full hover:bg-green-100 hover:text-green-600 transition-colors\",\n                                                    title: \"Phone\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Linkedin_Mail_MapPin_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 21\n                                                }, this),\n                                                member.contact.linkedin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: member.contact.linkedin,\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    className: \"p-2 bg-gray-100 rounded-full hover:bg-blue-100 hover:text-blue-600 transition-colors\",\n                                                    title: \"LinkedIn\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Linkedin_Mail_MapPin_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 15\n                            }, this)\n                        }, index, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-16 bg-gradient-to-r from-blue-50 to-green-50 rounded-2xl p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                    children: \"Our Team Culture\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 max-w-3xl mx-auto\",\n                                    children: \"We believe that a passionate, diverse, and dedicated team is the foundation of exceptional educational experiences.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-4 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Linkedin_Mail_MapPin_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"Continuous Learning\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"We invest in our team's growth and development\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Linkedin_Mail_MapPin_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"Collaboration\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"We work together to achieve common goals\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Linkedin_Mail_MapPin_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"Excellence\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"We strive for the highest standards in everything\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-orange-600 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Linkedin_Mail_MapPin_Phone_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"Adventure Spirit\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"We embrace challenges and new experiences\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n            lineNumber: 133,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TeamSection.tsx\",\n        lineNumber: 132,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/about/TeamSection.tsx\n");

/***/ }),

/***/ "(ssr)/./components/about/TimelineSection.tsx":
/*!**********************************************!*\
  !*** ./components/about/TimelineSection.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TimelineSection: () => (/* binding */ TimelineSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Globe_MapPin_Rocket_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Globe,MapPin,Rocket,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Globe_MapPin_Rocket_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Globe,MapPin,Rocket,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Globe_MapPin_Rocket_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Globe,MapPin,Rocket,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Globe_MapPin_Rocket_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Globe,MapPin,Rocket,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Globe_MapPin_Rocket_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Globe,MapPin,Rocket,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Globe_MapPin_Rocket_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Globe,MapPin,Rocket,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Globe_MapPin_Rocket_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Globe,MapPin,Rocket,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* __next_internal_client_entry_do_not_use__ TimelineSection auto */ \n\n\nconst TIMELINE_EVENTS = [\n    {\n        year: \"2009\",\n        title: \"The Beginning\",\n        description: \"Positive7 was founded with a vision to transform education through experiential learning and travel.\",\n        icon: _barrel_optimize_names_Award_BookOpen_Globe_MapPin_Rocket_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n        color: \"from-blue-500 to-blue-600\",\n        achievements: [\n            \"First educational tour to Manali\",\n            \"50 students from 3 schools\",\n            \"Foundation of safety-first approach\"\n        ]\n    },\n    {\n        year: \"2012\",\n        title: \"Expanding Horizons\",\n        description: \"Introduced new destinations and specialized programs for different age groups and learning objectives.\",\n        icon: _barrel_optimize_names_Award_BookOpen_Globe_MapPin_Rocket_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        color: \"from-green-500 to-green-600\",\n        achievements: [\n            \"Added Rishikesh and Dharamshala\",\n            \"500+ students annually\",\n            \"Partnership with 25+ schools\"\n        ]\n    },\n    {\n        year: \"2015\",\n        title: \"Innovation in Learning\",\n        description: \"Launched curriculum-aligned programs and introduced technology-enhanced learning experiences.\",\n        icon: _barrel_optimize_names_Award_BookOpen_Globe_MapPin_Rocket_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        color: \"from-purple-500 to-purple-600\",\n        achievements: [\n            \"Pre and post-trip learning modules\",\n            \"Digital portfolios for students\",\n            \"Teacher training workshops\"\n        ]\n    },\n    {\n        year: \"2018\",\n        title: \"Recognition & Growth\",\n        description: \"Received multiple industry awards and expanded to serve schools across Western India.\",\n        icon: _barrel_optimize_names_Award_BookOpen_Globe_MapPin_Rocket_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        color: \"from-yellow-500 to-yellow-600\",\n        achievements: [\n            \"Gujarat Tourism Excellence Award\",\n            \"100+ school partnerships\",\n            \"10,000+ students impacted\"\n        ]\n    },\n    {\n        year: \"2020\",\n        title: \"Adapting to Change\",\n        description: \"Pivoted to virtual learning experiences during the pandemic while maintaining our commitment to education.\",\n        icon: _barrel_optimize_names_Award_BookOpen_Globe_MapPin_Rocket_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        color: \"from-teal-500 to-teal-600\",\n        achievements: [\n            \"Virtual cultural exchanges\",\n            \"Online adventure challenges\",\n            \"Digital storytelling workshops\"\n        ]\n    },\n    {\n        year: \"2022\",\n        title: \"Sustainable Tourism\",\n        description: \"Launched eco-friendly initiatives and community-based tourism programs.\",\n        icon: _barrel_optimize_names_Award_BookOpen_Globe_MapPin_Rocket_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        color: \"from-green-600 to-emerald-600\",\n        achievements: [\n            \"Carbon-neutral trip options\",\n            \"Local community partnerships\",\n            \"Environmental education programs\"\n        ]\n    },\n    {\n        year: \"2024\",\n        title: \"Digital Transformation\",\n        description: \"Embracing technology to enhance the educational travel experience with our new digital platform.\",\n        icon: _barrel_optimize_names_Award_BookOpen_Globe_MapPin_Rocket_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        color: \"from-indigo-500 to-purple-600\",\n        achievements: [\n            \"AI-powered trip recommendations\",\n            \"Real-time parent updates\",\n            \"50,000+ students milestone\"\n        ]\n    }\n];\nfunction TimelineSection() {\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.2\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            x: -50\n        },\n        visible: {\n            opacity: 1,\n            x: 0\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"Our Journey\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TimelineSection.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                            children: \"From humble beginnings to becoming a leader in educational tourism, here's how we've grown and evolved over the years\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TimelineSection.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TimelineSection.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    whileInView: \"visible\",\n                    viewport: {\n                        once: true\n                    },\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-blue-500 to-green-500 hidden lg:block\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TimelineSection.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-12\",\n                            children: TIMELINE_EVENTS.map((event, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                    variants: itemVariants,\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute left-6 w-4 h-4 bg-gradient-to-r from-blue-600 to-green-600 rounded-full border-4 border-white shadow-lg z-10 hidden lg:block\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TimelineSection.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"lg:ml-16 bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col lg:flex-row gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: `w-16 h-16 bg-gradient-to-r ${event.color} rounded-full flex items-center justify-center mb-4`,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(event.icon, {\n                                                                    className: \"w-8 h-8 text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TimelineSection.tsx\",\n                                                                    lineNumber: 155,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TimelineSection.tsx\",\n                                                                lineNumber: 154,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center lg:text-left\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                                    children: event.year\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TimelineSection.tsx\",\n                                                                    lineNumber: 158,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TimelineSection.tsx\",\n                                                                lineNumber: 157,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TimelineSection.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-2xl font-bold text-gray-900 mb-3\",\n                                                                children: event.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TimelineSection.tsx\",\n                                                                lineNumber: 164,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-700 mb-6 leading-relaxed\",\n                                                                children: event.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TimelineSection.tsx\",\n                                                                lineNumber: 165,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"text-lg font-semibold text-gray-900 mb-3\",\n                                                                        children: \"Key Achievements:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TimelineSection.tsx\",\n                                                                        lineNumber: 169,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: event.achievements.map((achievement, achievementIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                className: \"flex items-start gap-3\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-2 h-2 bg-gradient-to-r from-blue-600 to-green-600 rounded-full mt-2 flex-shrink-0\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TimelineSection.tsx\",\n                                                                                        lineNumber: 173,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-700\",\n                                                                                        children: achievement\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TimelineSection.tsx\",\n                                                                                        lineNumber: 174,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, achievementIndex, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TimelineSection.tsx\",\n                                                                                lineNumber: 172,\n                                                                                columnNumber: 29\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TimelineSection.tsx\",\n                                                                        lineNumber: 170,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TimelineSection.tsx\",\n                                                                lineNumber: 168,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TimelineSection.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TimelineSection.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TimelineSection.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, event.year, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TimelineSection.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TimelineSection.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TimelineSection.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-16 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-blue-600 to-green-600 rounded-2xl p-8 text-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold mb-4\",\n                                children: \"Looking Ahead\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TimelineSection.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-100 max-w-3xl mx-auto leading-relaxed\",\n                                children: \"As we continue our journey, we remain committed to innovation, safety, and educational excellence. Our vision for the future includes expanding to new destinations, incorporating cutting-edge technology, and continuing to transform lives through the power of educational travel.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TimelineSection.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap justify-center gap-8 mt-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold\",\n                                                children: \"2025\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TimelineSection.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-blue-100\",\n                                                children: \"International Programs\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TimelineSection.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TimelineSection.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold\",\n                                                children: \"2026\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TimelineSection.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-blue-100\",\n                                                children: \"100,000 Students\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TimelineSection.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TimelineSection.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold\",\n                                                children: \"2027\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TimelineSection.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-blue-100\",\n                                                children: \"Pan-India Presence\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TimelineSection.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TimelineSection.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TimelineSection.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TimelineSection.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TimelineSection.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TimelineSection.tsx\",\n            lineNumber: 120,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\TimelineSection.tsx\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/about/TimelineSection.tsx\n");

/***/ }),

/***/ "(ssr)/./components/about/ValuesSection.tsx":
/*!********************************************!*\
  !*** ./components/about/ValuesSection.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ValuesSection: () => (/* binding */ ValuesSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Globe_Heart_Leaf_Lightbulb_Shield_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Globe,Heart,Leaf,Lightbulb,Shield,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Globe_Heart_Leaf_Lightbulb_Shield_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Globe,Heart,Leaf,Lightbulb,Shield,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Globe_Heart_Leaf_Lightbulb_Shield_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Globe,Heart,Leaf,Lightbulb,Shield,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Globe_Heart_Leaf_Lightbulb_Shield_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Globe,Heart,Leaf,Lightbulb,Shield,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Globe_Heart_Leaf_Lightbulb_Shield_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Globe,Heart,Leaf,Lightbulb,Shield,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/leaf.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Globe_Heart_Leaf_Lightbulb_Shield_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Globe,Heart,Leaf,Lightbulb,Shield,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Globe_Heart_Leaf_Lightbulb_Shield_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Globe,Heart,Leaf,Lightbulb,Shield,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Globe_Heart_Leaf_Lightbulb_Shield_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Globe,Heart,Leaf,Lightbulb,Shield,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* __next_internal_client_entry_do_not_use__ ValuesSection auto */ \n\n\nconst VALUES = [\n    {\n        icon: _barrel_optimize_names_BookOpen_Globe_Heart_Leaf_Lightbulb_Shield_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n        title: \"Passion for Learning\",\n        description: \"We believe that learning should be exciting, engaging, and transformative. Every experience we create is driven by our passion for education.\",\n        color: \"from-red-500 to-pink-500\"\n    },\n    {\n        icon: _barrel_optimize_names_BookOpen_Globe_Heart_Leaf_Lightbulb_Shield_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        title: \"Safety & Trust\",\n        description: \"The safety and well-being of our students is our top priority. We maintain the highest safety standards and build lasting trust with families.\",\n        color: \"from-blue-500 to-indigo-500\"\n    },\n    {\n        icon: _barrel_optimize_names_BookOpen_Globe_Heart_Leaf_Lightbulb_Shield_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: \"Innovation\",\n        description: \"We continuously innovate our programs to incorporate new learning methodologies and technologies that enhance the educational experience.\",\n        color: \"from-yellow-500 to-orange-500\"\n    },\n    {\n        icon: _barrel_optimize_names_BookOpen_Globe_Heart_Leaf_Lightbulb_Shield_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Collaboration\",\n        description: \"We work closely with educators, parents, and students to create customized experiences that meet specific learning objectives.\",\n        color: \"from-green-500 to-emerald-500\"\n    },\n    {\n        icon: _barrel_optimize_names_BookOpen_Globe_Heart_Leaf_Lightbulb_Shield_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Sustainability\",\n        description: \"We are committed to responsible tourism that respects local communities and preserves the environment for future generations.\",\n        color: \"from-green-600 to-teal-600\"\n    },\n    {\n        icon: _barrel_optimize_names_BookOpen_Globe_Heart_Leaf_Lightbulb_Shield_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"Excellence\",\n        description: \"We strive for excellence in every aspect of our service, from planning to execution, ensuring memorable and meaningful experiences.\",\n        color: \"from-purple-500 to-violet-500\"\n    },\n    {\n        icon: _barrel_optimize_names_BookOpen_Globe_Heart_Leaf_Lightbulb_Shield_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: \"Cultural Respect\",\n        description: \"We promote cultural understanding and respect, helping students become global citizens who appreciate diversity.\",\n        color: \"from-cyan-500 to-blue-500\"\n    },\n    {\n        icon: _barrel_optimize_names_BookOpen_Globe_Heart_Leaf_Lightbulb_Shield_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        title: \"Lifelong Learning\",\n        description: \"We inspire a love for lifelong learning, encouraging students to remain curious and open to new experiences throughout their lives.\",\n        color: \"from-indigo-500 to-purple-500\"\n    }\n];\nfunction ValuesSection() {\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 20\n        },\n        visible: {\n            opacity: 1,\n            y: 0\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"Our Core Values\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\ValuesSection.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                            children: \"These fundamental principles guide everything we do and shape the experiences we create for our students\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\ValuesSection.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\ValuesSection.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    whileInView: \"visible\",\n                    viewport: {\n                        once: true\n                    },\n                    className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                    children: VALUES.map((value, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            variants: itemVariants,\n                            className: \"group\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300 h-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `w-16 h-16 bg-gradient-to-r ${value.color} rounded-full flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(value.icon, {\n                                            className: \"w-8 h-8 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\ValuesSection.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\ValuesSection.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-bold text-gray-900 mb-4\",\n                                        children: value.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\ValuesSection.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 leading-relaxed\",\n                                        children: value.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\ValuesSection.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\ValuesSection.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 15\n                            }, this)\n                        }, index, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\ValuesSection.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\ValuesSection.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\ValuesSection.tsx\",\n            lineNumber: 84,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\about\\\\ValuesSection.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/about/ValuesSection.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/Button.tsx":
/*!**********************************!*\
  !*** ./components/ui/Button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Button)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* harmony import */ var _LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./LoadingSpinner */ \"(ssr)/./components/ui/LoadingSpinner.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst variantClasses = {\n    primary: \"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500\",\n    secondary: \"bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500\",\n    outline: \"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500\",\n    ghost: \"text-gray-700 hover:bg-gray-100 focus:ring-primary-500\"\n};\nconst sizeClasses = {\n    sm: \"px-3 py-2 text-sm\",\n    md: \"px-4 py-2 text-sm\",\n    lg: \"px-6 py-3 text-base\"\n};\nfunction Button({ variant = \"primary\", size = \"md\", loading = false, disabled, children, className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n        whileHover: {\n            scale: disabled || loading ? 1 : 1.02\n        },\n        whileTap: {\n            scale: disabled || loading ? 1 : 0.98\n        },\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none\", variantClasses[variant], sizeClasses[size], className),\n        disabled: disabled || loading,\n        ...props,\n        children: [\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                size: \"sm\",\n                color: variant === \"outline\" || variant === \"ghost\" ? \"gray\" : \"white\",\n                className: \"mr-2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\ui\\\\Button.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\ui\\\\Button.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/LoadingSpinner.tsx":
/*!******************************************!*\
  !*** ./components/ui/LoadingSpinner.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoadingSpinner)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst sizeClasses = {\n    sm: \"h-4 w-4\",\n    md: \"h-6 w-6\",\n    lg: \"h-8 w-8\",\n    xl: \"h-12 w-12\"\n};\nconst colorClasses = {\n    primary: \"border-primary-600\",\n    secondary: \"border-secondary-600\",\n    white: \"border-white\",\n    gray: \"border-gray-600\"\n};\nfunction LoadingSpinner({ size = \"md\", color = \"primary\", className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n        animate: {\n            rotate: 360\n        },\n        transition: {\n            duration: 1,\n            repeat: Infinity,\n            ease: \"linear\"\n        },\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border-2 border-t-transparent rounded-full\", sizeClasses[size], colorClasses[color], className)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\components\\\\ui\\\\LoadingSpinner.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/LoadingSpinner.tsx\n");

/***/ }),

/***/ "(ssr)/./contexts/AuthContext.tsx":
/*!**********************************!*\
  !*** ./contexts/AuthContext.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createClientSupabase)();\n    // Fetch user profile data\n    const fetchUserProfile = async (userId)=>{\n        try {\n            const { data, error } = await supabase.from(\"users\").select(\"*\").eq(\"id\", userId).single();\n            if (error) {\n                console.error(\"Error fetching user profile:\", error);\n                return null;\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error fetching user profile:\", error);\n            return null;\n        }\n    };\n    // Initialize auth state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeAuth = async ()=>{\n            try {\n                const { data: { session }, error } = await supabase.auth.getSession();\n                if (error) {\n                    console.error(\"Error getting session:\", error);\n                    setLoading(false);\n                    return;\n                }\n                setSession(session);\n                if (session?.user) {\n                    const userProfile = await fetchUserProfile(session.user.id);\n                    setUser(userProfile);\n                }\n            } catch (error) {\n                console.error(\"Error initializing auth:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        initializeAuth();\n        // Listen for auth changes\n        const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session)=>{\n            console.log(\"Auth state changed:\", event, session?.user?.id);\n            setSession(session);\n            if (session?.user) {\n                const userProfile = await fetchUserProfile(session.user.id);\n                setUser(userProfile);\n            } else {\n                setUser(null);\n            }\n            setLoading(false);\n        });\n        return ()=>{\n            subscription.unsubscribe();\n        };\n    }, []);\n    // Sign up function\n    const signUp = async (email, password, userData)=>{\n        try {\n            const { data, error } = await supabase.auth.signUp({\n                email,\n                password,\n                options: {\n                    data: {\n                        full_name: userData.full_name,\n                        phone: userData.phone\n                    }\n                }\n            });\n            if (error) {\n                return {\n                    data: null,\n                    error: error.message\n                };\n            }\n            // Create user profile\n            if (data.user) {\n                const { error: profileError } = await supabase.from(\"users\").insert({\n                    id: data.user.id,\n                    email: data.user.email,\n                    full_name: userData.full_name,\n                    phone: userData.phone,\n                    role: \"customer\"\n                });\n                if (profileError) {\n                    console.error(\"Error creating user profile:\", profileError);\n                    return {\n                        data: null,\n                        error: \"Failed to create user profile\"\n                    };\n                }\n            }\n            return {\n                data,\n                error: null\n            };\n        } catch (error) {\n            return {\n                data: null,\n                error: error.message\n            };\n        }\n    };\n    // Sign in function\n    const signIn = async (email, password)=>{\n        try {\n            const { data, error } = await supabase.auth.signInWithPassword({\n                email,\n                password\n            });\n            if (error) {\n                return {\n                    data: null,\n                    error: error.message\n                };\n            }\n            return {\n                data,\n                error: null\n            };\n        } catch (error) {\n            return {\n                data: null,\n                error: error.message\n            };\n        }\n    };\n    // Sign out function\n    const signOut = async ()=>{\n        try {\n            const { error } = await supabase.auth.signOut();\n            if (error) {\n                return {\n                    error: error.message\n                };\n            }\n            return {\n                error: null\n            };\n        } catch (error) {\n            return {\n                error: error.message\n            };\n        }\n    };\n    // Reset password function\n    const resetPassword = async (email)=>{\n        try {\n            const { error } = await supabase.auth.resetPasswordForEmail(email, {\n                redirectTo: `${window.location.origin}/auth/reset-password`\n            });\n            if (error) {\n                return {\n                    error: error.message\n                };\n            }\n            return {\n                error: null\n            };\n        } catch (error) {\n            return {\n                error: error.message\n            };\n        }\n    };\n    // Update profile function\n    const updateProfile = async (updates)=>{\n        if (!user) {\n            return {\n                data: null,\n                error: \"No user logged in\"\n            };\n        }\n        try {\n            const { data, error } = await supabase.from(\"users\").update({\n                ...updates,\n                updated_at: new Date().toISOString()\n            }).eq(\"id\", user.id).select().single();\n            if (error) {\n                return {\n                    data: null,\n                    error: error.message\n                };\n            }\n            setUser(data);\n            return {\n                data,\n                error: null\n            };\n        } catch (error) {\n            return {\n                data: null,\n                error: error.message\n            };\n        }\n    };\n    // Refresh user data\n    const refreshUser = async ()=>{\n        if (!session?.user) return;\n        const userProfile = await fetchUserProfile(session.user.id);\n        setUser(userProfile);\n    };\n    const value = {\n        user,\n        session,\n        loading,\n        signUp,\n        signIn,\n        signOut,\n        resetPassword,\n        updateProfile,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 252,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClientSupabase: () => (/* binding */ createClientSupabase),\n/* harmony export */   deleteFile: () => (/* binding */ deleteFile),\n/* harmony export */   getSession: () => (/* binding */ getSession),\n/* harmony export */   subscribeToTable: () => (/* binding */ subscribeToTable),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   unsubscribe: () => (/* binding */ unsubscribe),\n/* harmony export */   uploadFile: () => (/* binding */ uploadFile),\n/* harmony export */   withErrorHandling: () => (/* binding */ withErrorHandling)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// Environment variables with fallbacks for development\nconst supabaseUrl = \"https://placeholder.supabase.co\" || 0;\nconst supabaseAnonKey = \"placeholder-anon-key\" || 0;\n// Default client for general use\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// Client for use in client components (alias for compatibility)\nconst createClientSupabase = ()=>supabase;\n// Helper function to get user session (client-side)\nconst getSession = async ()=>{\n    const { data: { session }, error } = await supabase.auth.getSession();\n    if (error) {\n        console.error(\"Error getting session:\", error);\n        return null;\n    }\n    return session;\n};\n// Storage helpers\nconst uploadFile = async (bucket, path, file, options)=>{\n    const { data, error } = await supabase.storage.from(bucket).upload(path, file, options);\n    if (error) {\n        console.error(\"Error uploading file:\", error);\n        return {\n            data: null,\n            error\n        };\n    }\n    // Get public URL\n    const { data: { publicUrl } } = supabase.storage.from(bucket).getPublicUrl(path);\n    return {\n        data: {\n            ...data,\n            publicUrl\n        },\n        error: null\n    };\n};\nconst deleteFile = async (bucket, path)=>{\n    const { error } = await supabase.storage.from(bucket).remove([\n        path\n    ]);\n    if (error) {\n        console.error(\"Error deleting file:\", error);\n        return {\n            error\n        };\n    }\n    return {\n        error: null\n    };\n};\n// Database helpers\nconst withErrorHandling = async (operation)=>{\n    try {\n        const { data, error } = await operation();\n        if (error) {\n            console.error(\"Database error:\", error);\n            return {\n                data: null,\n                error: error.message || \"Database operation failed\"\n            };\n        }\n        return {\n            data,\n            error: null\n        };\n    } catch (err) {\n        console.error(\"Unexpected error:\", err);\n        return {\n            data: null,\n            error: \"An unexpected error occurred\"\n        };\n    }\n};\n// Real-time subscription helpers\nconst subscribeToTable = (table, callback, filter)=>{\n    let subscription = supabase.channel(`${table}_changes`).on(\"postgres_changes\", {\n        event: \"*\",\n        schema: \"public\",\n        table,\n        filter\n    }, callback).subscribe();\n    return subscription;\n};\nconst unsubscribe = (subscription)=>{\n    if (subscription) {\n        subscription.unsubscribe();\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateTripDuration: () => (/* binding */ calculateTripDuration),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatRelativeTime: () => (/* binding */ formatRelativeTime),\n/* harmony export */   generateBookingReference: () => (/* binding */ generateBookingReference),\n/* harmony export */   generateSEODescription: () => (/* binding */ generateSEODescription),\n/* harmony export */   generateSEOTitle: () => (/* binding */ generateSEOTitle),\n/* harmony export */   getDifficultyColor: () => (/* binding */ getDifficultyColor),\n/* harmony export */   getImageUrl: () => (/* binding */ getImageUrl),\n/* harmony export */   getInitials: () => (/* binding */ getInitials),\n/* harmony export */   getStatusColor: () => (/* binding */ getStatusColor),\n/* harmony export */   isValidUrl: () => (/* binding */ isValidUrl),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   slugify: () => (/* binding */ slugify),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   truncateText: () => (/* binding */ truncateText),\n/* harmony export */   validateEmail: () => (/* binding */ validateEmail),\n/* harmony export */   validatePhone: () => (/* binding */ validatePhone)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatCurrency(amount, currency = \"INR\") {\n    return new Intl.NumberFormat(\"en-IN\", {\n        style: \"currency\",\n        currency,\n        minimumFractionDigits: 0,\n        maximumFractionDigits: 0\n    }).format(amount);\n}\nfunction formatDate(date, options) {\n    const defaultOptions = {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\"\n    };\n    return new Intl.DateTimeFormat(\"en-IN\", {\n        ...defaultOptions,\n        ...options\n    }).format(typeof date === \"string\" ? new Date(date) : date);\n}\nfunction formatRelativeTime(date) {\n    const now = new Date();\n    const targetDate = typeof date === \"string\" ? new Date(date) : date;\n    const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000);\n    if (diffInSeconds < 60) return \"just now\";\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;\n    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`;\n    return formatDate(targetDate);\n}\nfunction slugify(text) {\n    return text.toLowerCase().replace(/[^\\w\\s-]/g, \"\").replace(/[\\s_-]+/g, \"-\").replace(/^-+|-+$/g, \"\");\n}\nfunction truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength).replace(/\\s+\\S*$/, \"\") + \"...\";\n}\nfunction generateBookingReference() {\n    const prefix = \"P7\";\n    const timestamp = Date.now().toString().slice(-6);\n    const random = Math.random().toString(36).substring(2, 6).toUpperCase();\n    return `${prefix}${timestamp}${random}`;\n}\nfunction validateEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\nfunction validatePhone(phone) {\n    const phoneRegex = /^[+]?[\\d\\s\\-\\(\\)]{10,}$/;\n    return phoneRegex.test(phone);\n}\nfunction debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\nfunction throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\nfunction getImageUrl(path, fallback) {\n    if (!path) return fallback || \"/images/placeholder.jpg\";\n    // If it's already a full URL, return as is\n    if (path.startsWith(\"http\")) return path;\n    // If it's a Supabase storage path\n    if (path.startsWith(\"/storage/\")) {\n        return `${\"https://placeholder.supabase.co\"}${path}`;\n    }\n    // If it's a relative path, make it absolute\n    if (path.startsWith(\"/\")) return path;\n    return `/${path}`;\n}\nfunction calculateTripDuration(startDate, endDate) {\n    const start = new Date(startDate);\n    const end = new Date(endDate);\n    const diffTime = Math.abs(end.getTime() - start.getTime());\n    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n}\nfunction getDifficultyColor(difficulty) {\n    switch(difficulty.toLowerCase()){\n        case \"easy\":\n            return \"text-green-600 bg-green-100\";\n        case \"moderate\":\n            return \"text-yellow-600 bg-yellow-100\";\n        case \"challenging\":\n            return \"text-orange-600 bg-orange-100\";\n        case \"extreme\":\n            return \"text-red-600 bg-red-100\";\n        default:\n            return \"text-gray-600 bg-gray-100\";\n    }\n}\nfunction getStatusColor(status) {\n    switch(status.toLowerCase()){\n        case \"confirmed\":\n        case \"completed\":\n        case \"approved\":\n            return \"text-green-600 bg-green-100\";\n        case \"pending\":\n            return \"text-yellow-600 bg-yellow-100\";\n        case \"cancelled\":\n        case \"rejected\":\n            return \"text-red-600 bg-red-100\";\n        case \"in_progress\":\n            return \"text-blue-600 bg-blue-100\";\n        default:\n            return \"text-gray-600 bg-gray-100\";\n    }\n}\nfunction generateSEOTitle(title, suffix) {\n    const baseSuffix = suffix || \"Positive7 - Educational Tours & Student Travel\";\n    return `${title} | ${baseSuffix}`;\n}\nfunction generateSEODescription(description, maxLength = 160) {\n    return truncateText(description, maxLength);\n}\nfunction isValidUrl(url) {\n    try {\n        new URL(url);\n        return true;\n    } catch  {\n        return false;\n    }\n}\nfunction getInitials(name) {\n    return name.split(\" \").map((word)=>word.charAt(0)).join(\"\").toUpperCase().slice(0, 2);\n}\nfunction sleep(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"4be39902af1f\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wb3NpdGl2ZTctdG91cmlzbS13ZWJzaXRlLy4vYXBwL2dsb2JhbHMuY3NzP2MyYjAiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI0YmUzOTkwMmFmMWZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/about/page.tsx":
/*!****************************!*\
  !*** ./app/about/page.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AboutPage),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(rsc)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Eye_Globe_Heart_Shield_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Eye,Globe,Heart,Shield,Target,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Eye_Globe_Heart_Shield_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Eye,Globe,Heart,Shield,Target,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Eye_Globe_Heart_Shield_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Eye,Globe,Heart,Shield,Target,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Eye_Globe_Heart_Shield_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Eye,Globe,Heart,Shield,Target,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Eye_Globe_Heart_Shield_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Eye,Globe,Heart,Shield,Target,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Eye_Globe_Heart_Shield_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Eye,Globe,Heart,Shield,Target,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Eye_Globe_Heart_Shield_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Eye,Globe,Heart,Shield,Target,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Eye_Globe_Heart_Shield_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Eye,Globe,Heart,Shield,Target,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(rsc)/./components/ui/Button.tsx\");\n/* harmony import */ var _components_about_TeamSection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/about/TeamSection */ \"(rsc)/./components/about/TeamSection.tsx\");\n/* harmony import */ var _components_about_TimelineSection__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/about/TimelineSection */ \"(rsc)/./components/about/TimelineSection.tsx\");\n/* harmony import */ var _components_about_ValuesSection__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/about/ValuesSection */ \"(rsc)/./components/about/ValuesSection.tsx\");\n/* harmony import */ var _components_about_StatsSection__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/about/StatsSection */ \"(rsc)/./components/about/StatsSection.tsx\");\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"About Us - Positive7 Educational Tours\",\n    description: \"Learn about Positive7's journey in creating transformative educational experiences for students across India. Discover our mission, values, and the team behind memorable learning adventures.\",\n    keywords: \"about positive7, educational tours company, student travel, team, mission, values, Gujarat tourism\",\n    openGraph: {\n        title: \"About Positive7 - Transforming Education Through Travel\",\n        description: \"Discover how Positive7 has been creating life-changing educational experiences for students since 2009.\",\n        images: [\n            \"/images/about-hero.jpg\"\n        ],\n        type: \"website\"\n    }\n};\nfunction AboutPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative py-20 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-blue-600/10 to-green-600/10\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 relative\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid lg:grid-cols-2 gap-12 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-5xl font-bold text-gray-900 mb-6\",\n                                            children: [\n                                                \"Transforming Education Through\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-green-600\",\n                                                    children: \" Travel\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 47,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl text-gray-700 mb-8 leading-relaxed\",\n                                            children: \"Since 2009, Positive7 has been pioneering experiential learning through carefully crafted educational tours. We believe that the world is the greatest classroom, and every journey is an opportunity to learn, grow, and discover.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    size: \"lg\",\n                                                    className: \"bg-gradient-to-r from-blue-600 to-green-600\",\n                                                    children: \"Our Story\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 54,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    variant: \"outline\",\n                                                    size: \"lg\",\n                                                    children: \"Meet Our Team\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 57,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative h-96 rounded-2xl overflow-hidden shadow-2xl\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                src: \"https://positive7.in/wp-content/uploads/2022/07/Manali-2.jpg\",\n                                                alt: \"Students on educational tour\",\n                                                fill: true,\n                                                className: \"object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -bottom-6 -left-6 bg-white rounded-xl p-6 shadow-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-3xl font-bold text-blue-600\",\n                                                    children: \"15+\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Years of Excellence\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 74,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -top-6 -right-6 bg-white rounded-xl p-6 shadow-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-3xl font-bold text-green-600\",\n                                                    children: \"50,000+\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 77,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Students Impacted\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 78,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                    children: \"Our Purpose\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                                    children: \"We exist to create transformative learning experiences that go beyond textbooks and classrooms\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 gap-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-2xl p-8 shadow-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-gradient-to-r from-blue-600 to-blue-700 rounded-full flex items-center justify-center mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Eye_Globe_Heart_Shield_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-8 h-8 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                            children: \"Our Mission\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700 leading-relaxed\",\n                                            children: \"To provide safe, educational, and transformative travel experiences that foster personal growth, cultural understanding, and lifelong learning. We are committed to creating memories that last a lifetime while ensuring the highest standards of safety and educational value.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-2xl p-8 shadow-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-gradient-to-r from-green-600 to-green-700 rounded-full flex items-center justify-center mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Eye_Globe_Heart_Shield_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-8 h-8 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                            children: \"Our Vision\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700 leading-relaxed\",\n                                            children: \"To be India's most trusted educational tour company, recognized for our innovative approach to experiential learning. We envision a world where every student has access to transformative travel experiences that shape them into confident, culturally aware global citizens.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_about_ValuesSection__WEBPACK_IMPORTED_MODULE_5__.ValuesSection, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_about_StatsSection__WEBPACK_IMPORTED_MODULE_6__.StatsSection, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_about_TimelineSection__WEBPACK_IMPORTED_MODULE_4__.TimelineSection, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_about_TeamSection__WEBPACK_IMPORTED_MODULE_3__.TeamSection, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-gradient-to-r from-blue-600 to-green-600\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold text-white mb-4\",\n                                    children: \"Why Choose Positive7?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-blue-100 max-w-3xl mx-auto\",\n                                    children: \"We're not just a tour company - we're your partners in creating life-changing educational experiences\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-3 gap-8\",\n                            children: [\n                                {\n                                    icon: _barrel_optimize_names_Award_BookOpen_Eye_Globe_Heart_Shield_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                                    title: \"Safety First\",\n                                    description: \"Comprehensive safety protocols, trained guides, and 24/7 support ensure peace of mind for parents and educators.\"\n                                },\n                                {\n                                    icon: _barrel_optimize_names_Award_BookOpen_Eye_Globe_Heart_Shield_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                                    title: \"Educational Excellence\",\n                                    description: \"Every trip is designed with clear learning objectives, pre and post-trip activities, and curriculum alignment.\"\n                                },\n                                {\n                                    icon: _barrel_optimize_names_Award_BookOpen_Eye_Globe_Heart_Shield_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                                    title: \"Personal Growth\",\n                                    description: \"We focus on building confidence, independence, and life skills through carefully designed challenges and experiences.\"\n                                },\n                                {\n                                    icon: _barrel_optimize_names_Award_BookOpen_Eye_Globe_Heart_Shield_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                                    title: \"Cultural Immersion\",\n                                    description: \"Authentic local experiences that foster cultural understanding and global citizenship.\"\n                                },\n                                {\n                                    icon: _barrel_optimize_names_Award_BookOpen_Eye_Globe_Heart_Shield_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                                    title: \"Expert Team\",\n                                    description: \"Experienced educators and travel professionals who understand both learning and adventure.\"\n                                },\n                                {\n                                    icon: _barrel_optimize_names_Award_BookOpen_Eye_Globe_Heart_Shield_Target_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                                    title: \"Proven Track Record\",\n                                    description: \"15+ years of excellence with thousands of successful trips and satisfied students and schools.\"\n                                }\n                            ].map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                className: \"w-8 h-8 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-white mb-4\",\n                                            children: feature.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-blue-100 leading-relaxed\",\n                                            children: feature.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto px-4 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl font-bold text-gray-900 mb-6\",\n                            children: \"Ready to Create Unforgettable Learning Experiences?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 mb-8\",\n                            children: \"Join thousands of students who have transformed their learning through our educational tours\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    size: \"lg\",\n                                    className: \"bg-gradient-to-r from-blue-600 to-green-600\",\n                                    children: \"Plan Your Trip\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    variant: \"outline\",\n                                    size: \"lg\",\n                                    children: \"Download Brochure\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\about\\\\page.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/about/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_800_variable_font_poppins_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\",\"800\"],\"variable\":\"--font-poppins\",\"display\":\"swap\"}],\"variableName\":\"poppins\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Poppins\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\",\\\"800\\\"],\\\"variable\\\":\\\"--font-poppins\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"poppins\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_800_variable_font_poppins_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_800_variable_font_poppins_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/constants */ \"(rsc)/./lib/constants.ts\");\n\n\n\n\n\n\nconst metadata = {\n    title: {\n        default: `${_lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.name} - ${_lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.tagline}`,\n        template: `%s | ${_lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.name}`\n    },\n    description: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.description,\n    keywords: [\n        \"educational tours\",\n        \"student travel\",\n        \"experiential learning\",\n        \"adventure camps\",\n        \"school trips\",\n        \"Gujarat tourism\",\n        \"positive7\",\n        \"educational trips\",\n        \"student tours\",\n        \"CAS projects\",\n        \"workshops\",\n        \"picnics\"\n    ],\n    authors: [\n        {\n            name: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.name,\n            url: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.website\n        }\n    ],\n    creator: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.name,\n    publisher: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.name,\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL(_lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.website),\n    alternates: {\n        canonical: \"/\"\n    },\n    openGraph: {\n        type: \"website\",\n        locale: \"en_IN\",\n        url: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.website,\n        siteName: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.name,\n        title: `${_lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.name} - ${_lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.tagline}`,\n        description: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.description,\n        images: [\n            {\n                url: \"/images/og-image.jpg\",\n                width: 1200,\n                height: 630,\n                alt: `${_lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.name} - Educational Tours & Student Travel`\n            }\n        ]\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: `${_lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.name} - ${_lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.tagline}`,\n        description: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.description,\n        images: [\n            \"/images/twitter-image.jpg\"\n        ],\n        creator: \"@positive7ind\"\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    verification: {\n        google: \"your-google-verification-code\"\n    },\n    category: \"travel\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_800_variable_font_poppins_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_5___default().variable)}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\",\n                        sizes: \"any\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/icon.svg\",\n                        type: \"image/svg+xml\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#0ea5e9\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1, maximum-scale=5\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"font-sans antialiased\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex min-h-screen flex-col\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        type: \"application/ld+json\",\n                        dangerouslySetInnerHTML: {\n                            __html: JSON.stringify({\n                                \"@context\": \"https://schema.org\",\n                                \"@type\": \"TravelAgency\",\n                                name: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.name,\n                                description: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.description,\n                                url: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.website,\n                                logo: `${_lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.website}/images/positive7-logo.png`,\n                                image: `${_lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.website}/images/og-image.jpg`,\n                                telephone: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.phone,\n                                email: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.email,\n                                address: {\n                                    \"@type\": \"PostalAddress\",\n                                    streetAddress: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.COMPANY_INFO.address,\n                                    addressLocality: \"Ahmedabad\",\n                                    addressRegion: \"Gujarat\",\n                                    postalCode: \"380015\",\n                                    addressCountry: \"IN\"\n                                },\n                                sameAs: [\n                                    \"https://www.facebook.com/positive7.ind\",\n                                    \"https://www.instagram.com/positive.seven/\",\n                                    \"https://www.youtube.com/channel/UC22w2efe7oZCmEcrU8g2xnw/featured\"\n                                ],\n                                serviceType: [\n                                    \"Educational Tours\",\n                                    \"Student Travel\",\n                                    \"Adventure Camps\",\n                                    \"Experiential Learning\",\n                                    \"School Trips\",\n                                    \"CAS Projects\",\n                                    \"Workshops\"\n                                ],\n                                areaServed: {\n                                    \"@type\": \"Country\",\n                                    name: \"India\"\n                                }\n                            })\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\layout.tsx\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/about/StatsSection.tsx":
/*!*******************************************!*\
  !*** ./components/about/StatsSection.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   StatsSection: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\projects\p7-comprehensive\components\about\StatsSection.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\projects\p7-comprehensive\components\about\StatsSection.tsx#StatsSection`);


/***/ }),

/***/ "(rsc)/./components/about/TeamSection.tsx":
/*!******************************************!*\
  !*** ./components/about/TeamSection.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   TeamSection: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\projects\p7-comprehensive\components\about\TeamSection.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\projects\p7-comprehensive\components\about\TeamSection.tsx#TeamSection`);


/***/ }),

/***/ "(rsc)/./components/about/TimelineSection.tsx":
/*!**********************************************!*\
  !*** ./components/about/TimelineSection.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   TimelineSection: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\projects\p7-comprehensive\components\about\TimelineSection.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\projects\p7-comprehensive\components\about\TimelineSection.tsx#TimelineSection`);


/***/ }),

/***/ "(rsc)/./components/about/ValuesSection.tsx":
/*!********************************************!*\
  !*** ./components/about/ValuesSection.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ValuesSection: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\projects\p7-comprehensive\components\about\ValuesSection.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\projects\p7-comprehensive\components\about\ValuesSection.tsx#ValuesSection`);


/***/ }),

/***/ "(rsc)/./components/ui/Button.tsx":
/*!**********************************!*\
  !*** ./components/ui/Button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\projects\p7-comprehensive\components\ui\Button.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./contexts/AuthContext.tsx":
/*!**********************************!*\
  !*** ./contexts/AuthContext.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e1),
/* harmony export */   useAuth: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\projects\p7-comprehensive\contexts\AuthContext.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\projects\p7-comprehensive\contexts\AuthContext.tsx#useAuth`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\projects\p7-comprehensive\contexts\AuthContext.tsx#AuthProvider`);


/***/ }),

/***/ "(rsc)/./lib/constants.ts":
/*!**************************!*\
  !*** ./lib/constants.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BOOKING_STATUS: () => (/* binding */ BOOKING_STATUS),\n/* harmony export */   COMPANY_INFO: () => (/* binding */ COMPANY_INFO),\n/* harmony export */   CONTACT_FORM_TYPES: () => (/* binding */ CONTACT_FORM_TYPES),\n/* harmony export */   DESTINATIONS: () => (/* binding */ DESTINATIONS),\n/* harmony export */   EDUCATIONAL_EXCELLENCE: () => (/* binding */ EDUCATIONAL_EXCELLENCE),\n/* harmony export */   FEATURED_TRIPS: () => (/* binding */ FEATURED_TRIPS),\n/* harmony export */   NAVIGATION_ITEMS: () => (/* binding */ NAVIGATION_ITEMS),\n/* harmony export */   QUICK_LINKS: () => (/* binding */ QUICK_LINKS),\n/* harmony export */   SAINT_AUGUSTINE_QUOTE: () => (/* binding */ SAINT_AUGUSTINE_QUOTE),\n/* harmony export */   SOCIAL_LINKS: () => (/* binding */ SOCIAL_LINKS),\n/* harmony export */   TESTIMONIALS: () => (/* binding */ TESTIMONIALS),\n/* harmony export */   TRIP_CATEGORIES: () => (/* binding */ TRIP_CATEGORIES),\n/* harmony export */   TRIP_DIFFICULTIES: () => (/* binding */ TRIP_DIFFICULTIES),\n/* harmony export */   UDBHAV_INFO: () => (/* binding */ UDBHAV_INFO),\n/* harmony export */   USER_ROLES: () => (/* binding */ USER_ROLES)\n/* harmony export */ });\n// Constants based on scraped content from positive7.in\nconst COMPANY_INFO = {\n    name: \"Positive7\",\n    tagline: \"Bring Learning To Life\",\n    heroQuote: \"The Best Way To Be Lost & Found At The Same Time Is To TRAVEL\",\n    description: \"Positive7 is a Gujarat Tourism affiliated outbound experiential learning company organizing, educational trips, students tour, CAS Projects, Picnics, adventure camps & Workshops.\",\n    address: \"904, SHIVALIK HIGHSTREET, LANDMARK: B/S, ITC NARMADA HOTEL MANSI – KESHAVBAUG ROAD ,VASTRAPUR, AHMEDABAD-380015.\",\n    phone: \"+91 78780 05500\",\n    alternatePhone: \"+91 7265005500\",\n    email: \"<EMAIL>\",\n    whatsapp: \"+917878005500\",\n    website: \"https://positive7.in\",\n    logo: \"/images/positive7-logo.png\"\n};\nconst SOCIAL_LINKS = {\n    facebook: \"https://www.facebook.com/positive7.ind\",\n    instagram: \"https://www.instagram.com/positive.seven/\",\n    youtube: \"https://www.youtube.com/channel/UC22w2efe7oZCmEcrU8g2xnw/featured\",\n    whatsapp: \"http://wa.me/+917878005500?text=Hi%20i%20have%20enquiry\"\n};\nconst NAVIGATION_ITEMS = [\n    {\n        name: \"Home\",\n        href: \"/\"\n    },\n    {\n        name: \"About\",\n        href: \"/about\"\n    },\n    {\n        name: \"Services\",\n        href: \"/services\"\n    },\n    {\n        name: \"Trips\",\n        href: \"/trips\"\n    },\n    {\n        name: \"Gallery\",\n        href: \"/gallery\"\n    },\n    {\n        name: \"Blog\",\n        href: \"/blog\"\n    },\n    {\n        name: \"Contact\",\n        href: \"/contact\"\n    },\n    {\n        name: \"Udbhav\",\n        href: \"/udbhav\"\n    }\n];\nconst QUICK_LINKS = [\n    {\n        name: \"About Us\",\n        href: \"/about\"\n    },\n    {\n        name: \"Gallery\",\n        href: \"/gallery\"\n    },\n    {\n        name: \"Blog\",\n        href: \"/blog\"\n    },\n    {\n        name: \"Trips Photos\",\n        href: \"/trips-photos\"\n    },\n    {\n        name: \"Terms & Conditions\",\n        href: \"/terms-conditions\"\n    },\n    {\n        name: \"Privacy Policy\",\n        href: \"/privacy-policy\"\n    }\n];\n// Scraped trip data from positive7.in\nconst FEATURED_TRIPS = [\n    {\n        id: \"manali\",\n        title: \"Manali\",\n        duration: \"9 Days 8 Nights\",\n        description: \"The most popular hill stations in Himachal, Manali offers the most magnificent views of the Pir Panjal and the Dhauladhar ranges covered with snow for most of the year.\",\n        image: \"https://positive7.in/wp-content/uploads/2025/01/gettyimages-**********-612x612-1.jpg\",\n        difficulty: \"moderate\",\n        category: \"Hill Station\",\n        destination: \"Himachal Pradesh\"\n    },\n    {\n        id: \"rishikesh\",\n        title: \"Rishikesh\",\n        duration: \"7 Days 6 Nights\",\n        description: 'Rishikesh, nestled in the foothills of the Himalayas along the banks of the Ganges River, is a captivating destination known as the \"Yoga Capital of the World\"',\n        image: \"https://positive7.in/wp-content/uploads/2022/09/dusk-time-rishikesh-holy-town-travel-destination-india-1024x684.jpg\",\n        difficulty: \"easy\",\n        category: \"Spiritual\",\n        destination: \"Uttarakhand\"\n    },\n    {\n        id: \"tirthan-valley\",\n        title: \"Tirthan Valley & Jibhi\",\n        duration: \"9 Days 8 Nights\",\n        description: \"Tirthan Valley: Serene Himalayan retreat with lush landscapes and access to the Great Himalayan National Park.\",\n        image: \"https://positive7.in/wp-content/uploads/2024/11/TIRTHAN-VALLEY-JIBHI-1024x697.webp\",\n        difficulty: \"moderate\",\n        category: \"Nature\",\n        destination: \"Himachal Pradesh\"\n    },\n    {\n        id: \"dharamshala\",\n        title: \"Dharamshala\",\n        duration: \"10 Days 9 Nights\",\n        description: \"Amritsar offers culture and history, Dharamshala provides Tibetan serenity, and Dalhousie delights with colonial charm and scenic beauty.\",\n        image: \"https://positive7.in/wp-content/uploads/2024/11/AMRITSAR-DHARAMSHALA-MCLEODGANJ-TRIUND-DALHOUSIE.webp\",\n        difficulty: \"moderate\",\n        category: \"Cultural\",\n        destination: \"Punjab & Himachal Pradesh\"\n    },\n    {\n        id: \"rajpura\",\n        title: \"Rajpura\",\n        duration: \"3 Days 2 Nights\",\n        description: \"Sundha Mata (Rajpura) is a small village located in Jalore district of Rajasthan. It is 64 km away from Mount Abu. This place is famous for Sundha Mata temple.\",\n        image: \"https://positive7.in/wp-content/uploads/2025/04/1602740643_Rajasthan_Adventure_Resort1.webp\",\n        difficulty: \"easy\",\n        category: \"Religious\",\n        destination: \"Rajasthan\"\n    },\n    {\n        id: \"brigu-lake\",\n        title: \"Brigu Lake\",\n        duration: \"9 Days 8 Nights\",\n        description: \"The Brigu Lake trek, located near Manali in Himachal Pradesh, is a stunning adventure that takes you through lush forests, picturesque meadows, and breathtaking mountain views.\",\n        image: \"https://positive7.in/wp-content/uploads/2024/11/BRIGU-LAKE2.webp\",\n        difficulty: \"challenging\",\n        category: \"Trekking\",\n        destination: \"Himachal Pradesh\"\n    }\n];\n// Scraped testimonials from positive7.in\nconst TESTIMONIALS = [\n    {\n        id: 1,\n        name: \"Krupa Bhatt\",\n        role: \"Student\",\n        content: \"If i could rewind those moments those days those experiences again then I surely would may it get less adventures may it get less thrilling and fun but still I would because the past 6 days were a whole sum of adventure and an unforgettable piece of my life.\",\n        rating: 5,\n        image: \"/images/testimonials/krupa-bhatt.jpg\"\n    },\n    {\n        id: 2,\n        name: \"Kavita Pillai\",\n        role: \"Parent\",\n        content: \"Trekking transforms lives, I had heard this, but for me, I can see those changes in my Son, he has impacted greatly, The transformations has been profound, he loved the trekking experience of his camping trip to Manali with Team Positive 7\",\n        rating: 5,\n        image: \"/images/testimonials/kavita-pillai.jpg\"\n    },\n    {\n        id: 3,\n        name: \"Hetal Vora\",\n        role: \"Parent\",\n        content: \"Kids had fun. The coordinators, arrangements, activities, stay place was planned where kids can have maximum enjoyment. Definitely recommended even kid has never stay away from parents for a day.\",\n        rating: 5,\n        image: \"/images/testimonials/hetal-vora.jpg\"\n    },\n    {\n        id: 4,\n        name: \"Sachin Mehta\",\n        role: \"Parent\",\n        content: \"Positive7 is a place of positivity and encouragement. The trip is well organized and has comfortable journey throughout. The activities are very enthusiastic and cheering and constant updates are given to the parents about the trip and the children.\",\n        rating: 5,\n        image: \"/images/testimonials/sachin-mehta.jpg\"\n    },\n    {\n        id: 5,\n        name: \"Rani Jaiswal\",\n        role: \"Educator\",\n        content: \"It is a Positive group that spreads positivity in the lives of people connected with it. A wonderful group that gave me beautiful moments to cherish in my life. I got one such good opportunity to be with them during our schl trip with our Student at Borsad, camp dilly.\",\n        rating: 5,\n        image: \"/images/testimonials/rani-jaiswal.jpg\"\n    },\n    {\n        id: 6,\n        name: \"Shirali Shah\",\n        role: \"Parent\",\n        content: \"Positive7 is such a wonderful team and great example of super team work. Super experience and lot's of fun with discipline. My son learn so much new things. I have send my son for the first time with you and the experience was awesome. Thank you so much.\",\n        rating: 5,\n        image: \"/images/testimonials/shirali-shah.jpg\"\n    }\n];\nconst TRIP_CATEGORIES = [\n    \"Hill Station\",\n    \"Spiritual\",\n    \"Nature\",\n    \"Cultural\",\n    \"Religious\",\n    \"Trekking\",\n    \"Adventure\",\n    \"Wildlife\",\n    \"Historical\"\n];\nconst TRIP_DIFFICULTIES = [\n    {\n        value: \"easy\",\n        label: \"Easy\",\n        color: \"green\"\n    },\n    {\n        value: \"moderate\",\n        label: \"Moderate\",\n        color: \"yellow\"\n    },\n    {\n        value: \"challenging\",\n        label: \"Challenging\",\n        color: \"orange\"\n    },\n    {\n        value: \"extreme\",\n        label: \"Extreme\",\n        color: \"red\"\n    }\n];\nconst DESTINATIONS = [\n    \"Himachal Pradesh\",\n    \"Uttarakhand\",\n    \"Rajasthan\",\n    \"Punjab\",\n    \"Gujarat\",\n    \"Maharashtra\",\n    \"Goa\",\n    \"Kerala\",\n    \"Karnataka\",\n    \"Tamil Nadu\"\n];\nconst UDBHAV_INFO = {\n    title: \"Udbhav: Exploring Rural Life\",\n    description: 'Taking inspiration from these quotes we at \"Positive7\" have come up with an initiative known as \"Udbhav\". It will be a drive to connect the rural and urban areas through culture, art and traditions.',\n    images: [\n        \"https://positive7.in/wp-content/uploads/2022/07/Udbhav.jpg\",\n        \"https://positive7.in/wp-content/uploads/2022/07/Udbhav-2-scaled.jpg\",\n        \"https://positive7.in/wp-content/uploads/2022/07/Udbhav-1-scaled.jpg\",\n        \"https://positive7.in/wp-content/uploads/2022/07/Udbhav-3-1024x467.jpg\"\n    ]\n};\nconst SAINT_AUGUSTINE_QUOTE = {\n    text: '\"The world is a book, and those who do not travel read only one page.\"',\n    author: \"Saint Augustine\",\n    image: \"https://positive7.in/wp-content/uploads/2018/11/quote-1.png\"\n};\nconst EDUCATIONAL_EXCELLENCE = {\n    title: \"Educational Excellence\",\n    description: \"We believe it's not just the exposure to new places that changes student's lives, but also the kind of experience they have during that exposure. That's why we work with you to build programme content that meets your travel / learning goals.\"\n};\nconst CONTACT_FORM_TYPES = [\n    \"General Inquiry\",\n    \"Trip Booking\",\n    \"Custom Trip Request\",\n    \"Group Booking\",\n    \"Educational Program\",\n    \"Udbhav Initiative\",\n    \"Partnership\",\n    \"Other\"\n];\nconst BOOKING_STATUS = {\n    PENDING: \"pending\",\n    CONFIRMED: \"confirmed\",\n    CANCELLED: \"cancelled\",\n    COMPLETED: \"completed\"\n};\nconst USER_ROLES = {\n    CUSTOMER: \"customer\",\n    ADMIN: \"admin\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./lib/constants.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/framer-motion","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fabout%2Fpage&page=%2Fabout%2Fpage&appPaths=%2Fabout%2Fpage&pagePath=private-next-app-dir%2Fabout%2Fpage.tsx&appDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();