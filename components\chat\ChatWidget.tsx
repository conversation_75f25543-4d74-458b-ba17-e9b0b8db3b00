'use client'

import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  MessageCircle, 
  X, 
  Send, 
  Paperclip, 
  Phone, 
  Mail,
  Clock,
  User,
  Bot,
  Minimize2,
  Maximize2
} from 'lucide-react'
import Button from '@/components/ui/Button'

interface Message {
  id: string
  text: string
  sender: 'user' | 'agent' | 'bot'
  timestamp: Date
  type: 'text' | 'image' | 'file'
  status?: 'sending' | 'sent' | 'delivered' | 'read'
}

interface ChatWidgetProps {
  companyName?: string
  supportHours?: string
  contactInfo?: {
    phone: string
    email: string
    whatsapp?: string
  }
}

const INITIAL_MESSAGES: Message[] = [
  {
    id: '1',
    text: 'Hello! Welcome to Positive7 Tours. How can I help you today?',
    sender: 'bot',
    timestamp: new Date(),
    type: 'text',
    status: 'delivered'
  }
]

const QUICK_REPLIES = [
  'Trip information',
  'Booking inquiry',
  'Payment options',
  'Cancellation policy',
  'Group discounts',
  'Custom packages'
]

export function ChatWidget({ 
  companyName = 'Positive7 Tours',
  supportHours = '9:00 AM - 8:00 PM IST',
  contactInfo = {
    phone: '+91 78780 05500',
    email: '<EMAIL>',
    whatsapp: '+91 78780 05500'
  }
}: ChatWidgetProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [isMinimized, setIsMinimized] = useState(false)
  const [messages, setMessages] = useState<Message[]>(INITIAL_MESSAGES)
  const [inputText, setInputText] = useState('')
  const [isTyping, setIsTyping] = useState(false)
  const [isOnline, setIsOnline] = useState(true)
  const [unreadCount, setUnreadCount] = useState(0)
  
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  // Focus input when chat opens
  useEffect(() => {
    if (isOpen && !isMinimized) {
      inputRef.current?.focus()
    }
  }, [isOpen, isMinimized])

  // Simulate agent typing and responses
  const simulateAgentResponse = (userMessage: string) => {
    setIsTyping(true)
    
    setTimeout(() => {
      setIsTyping(false)
      
      let response = ''
      const lowerMessage = userMessage.toLowerCase()
      
      if (lowerMessage.includes('trip') || lowerMessage.includes('tour')) {
        response = 'Great! We offer amazing educational tours to destinations like Manali, Rishikesh, and Dharamshala. Which destination interests you the most?'
      } else if (lowerMessage.includes('booking') || lowerMessage.includes('book')) {
        response = 'I\'d be happy to help you with booking! You can book online or call us at +91 78780 05500. Do you have a specific trip in mind?'
      } else if (lowerMessage.includes('price') || lowerMessage.includes('cost')) {
        response = 'Our trip prices vary by destination and duration. For example, our Manali tour is ₹27,500 per person. Would you like detailed pricing for any specific trip?'
      } else if (lowerMessage.includes('payment')) {
        response = 'We accept payments through Razorpay (UPI, Cards, Net Banking) and offer flexible payment options - 50% advance or full payment with 5% discount.'
      } else {
        response = 'Thank you for your message! Our team will get back to you shortly. You can also call us directly at +91 78780 05500 for immediate assistance.'
      }
      
      addMessage(response, 'agent')
    }, 1000 + Math.random() * 2000) // Random delay between 1-3 seconds
  }

  const addMessage = (text: string, sender: 'user' | 'agent' | 'bot') => {
    const newMessage: Message = {
      id: Date.now().toString(),
      text,
      sender,
      timestamp: new Date(),
      type: 'text',
      status: sender === 'user' ? 'sending' : 'delivered'
    }
    
    setMessages(prev => [...prev, newMessage])
    
    // Update message status after a delay
    if (sender === 'user') {
      setTimeout(() => {
        setMessages(prev => 
          prev.map(msg => 
            msg.id === newMessage.id 
              ? { ...msg, status: 'delivered' }
              : msg
          )
        )
      }, 1000)
    }
    
    // Increment unread count if chat is closed
    if (!isOpen && sender !== 'user') {
      setUnreadCount(prev => prev + 1)
    }
  }

  const sendMessage = () => {
    if (!inputText.trim()) return
    
    addMessage(inputText, 'user')
    simulateAgentResponse(inputText)
    setInputText('')
  }

  const handleQuickReply = (reply: string) => {
    addMessage(reply, 'user')
    simulateAgentResponse(reply)
  }

  const openChat = () => {
    setIsOpen(true)
    setUnreadCount(0)
  }

  const closeChat = () => {
    setIsOpen(false)
    setIsMinimized(false)
  }

  const toggleMinimize = () => {
    setIsMinimized(!isMinimized)
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-IN', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: true 
    })
  }

  return (
    <>
      {/* Chat Button */}
      <AnimatePresence>
        {!isOpen && (
          <motion.button
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0, opacity: 0 }}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={openChat}
            className="fixed bottom-6 right-6 z-40 w-16 h-16 bg-gradient-to-r from-blue-600 to-green-600 text-white rounded-full shadow-lg hover:shadow-xl transition-shadow flex items-center justify-center"
          >
            <MessageCircle className="w-6 h-6" />
            {unreadCount > 0 && (
              <div className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-bold">
                {unreadCount > 9 ? '9+' : unreadCount}
              </div>
            )}
          </motion.button>
        )}
      </AnimatePresence>

      {/* Chat Window */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: 100, scale: 0.8 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 100, scale: 0.8 }}
            className={`fixed bottom-6 right-6 z-50 bg-white rounded-2xl shadow-2xl border border-gray-200 overflow-hidden ${
              isMinimized ? 'w-80 h-16' : 'w-80 h-96'
            }`}
          >
            {/* Header */}
            <div className="bg-gradient-to-r from-blue-600 to-green-600 text-white p-4 flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                  <MessageCircle className="w-4 h-4" />
                </div>
                <div>
                  <h3 className="font-medium text-sm">{companyName}</h3>
                  <div className="flex items-center gap-1 text-xs text-white/80">
                    <div className={`w-2 h-2 rounded-full ${isOnline ? 'bg-green-400' : 'bg-gray-400'}`} />
                    <span>{isOnline ? 'Online' : 'Offline'}</span>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-1">
                <button
                  onClick={toggleMinimize}
                  className="p-1 hover:bg-white/20 rounded transition-colors"
                >
                  {isMinimized ? (
                    <Maximize2 className="w-4 h-4" />
                  ) : (
                    <Minimize2 className="w-4 h-4" />
                  )}
                </button>
                <button
                  onClick={closeChat}
                  className="p-1 hover:bg-white/20 rounded transition-colors"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            </div>

            {!isMinimized && (
              <>
                {/* Messages */}
                <div className="h-64 overflow-y-auto p-4 space-y-3">
                  {messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                    >
                      <div className={`max-w-[80%] ${message.sender === 'user' ? 'order-2' : 'order-1'}`}>
                        <div
                          className={`px-3 py-2 rounded-lg text-sm ${
                            message.sender === 'user'
                              ? 'bg-blue-600 text-white'
                              : 'bg-gray-100 text-gray-900'
                          }`}
                        >
                          {message.text}
                        </div>
                        <div className={`text-xs text-gray-500 mt-1 ${
                          message.sender === 'user' ? 'text-right' : 'text-left'
                        }`}>
                          {formatTime(message.timestamp)}
                          {message.sender === 'user' && message.status && (
                            <span className="ml-1">
                              {message.status === 'sending' && '⏳'}
                              {message.status === 'sent' && '✓'}
                              {message.status === 'delivered' && '✓✓'}
                              {message.status === 'read' && '✓✓'}
                            </span>
                          )}
                        </div>
                      </div>
                      {message.sender !== 'user' && (
                        <div className="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center mr-2 flex-shrink-0">
                          {message.sender === 'bot' ? (
                            <Bot className="w-3 h-3 text-gray-600" />
                          ) : (
                            <User className="w-3 h-3 text-gray-600" />
                          )}
                        </div>
                      )}
                    </div>
                  ))}
                  
                  {/* Typing Indicator */}
                  {isTyping && (
                    <div className="flex justify-start">
                      <div className="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center mr-2 flex-shrink-0">
                        <User className="w-3 h-3 text-gray-600" />
                      </div>
                      <div className="bg-gray-100 px-3 py-2 rounded-lg">
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                        </div>
                      </div>
                    </div>
                  )}
                  
                  <div ref={messagesEndRef} />
                </div>

                {/* Quick Replies */}
                {messages.length <= 2 && (
                  <div className="px-4 pb-2">
                    <div className="text-xs text-gray-500 mb-2">Quick replies:</div>
                    <div className="flex flex-wrap gap-1">
                      {QUICK_REPLIES.slice(0, 3).map((reply) => (
                        <button
                          key={reply}
                          onClick={() => handleQuickReply(reply)}
                          className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full hover:bg-gray-200 transition-colors"
                        >
                          {reply}
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Input */}
                <div className="border-t border-gray-200 p-4">
                  <div className="flex items-center gap-2">
                    <input
                      ref={inputRef}
                      type="text"
                      value={inputText}
                      onChange={(e) => setInputText(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                      placeholder="Type your message..."
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                    />
                    <button
                      onClick={sendMessage}
                      disabled={!inputText.trim()}
                      className="p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      <Send className="w-4 h-4" />
                    </button>
                  </div>
                  
                  {/* Contact Options */}
                  <div className="flex items-center justify-center gap-4 mt-3 pt-3 border-t border-gray-100">
                    <a
                      href={`tel:${contactInfo.phone}`}
                      className="flex items-center gap-1 text-xs text-gray-600 hover:text-blue-600 transition-colors"
                    >
                      <Phone className="w-3 h-3" />
                      Call
                    </a>
                    <a
                      href={`mailto:${contactInfo.email}`}
                      className="flex items-center gap-1 text-xs text-gray-600 hover:text-blue-600 transition-colors"
                    >
                      <Mail className="w-3 h-3" />
                      Email
                    </a>
                    <div className="flex items-center gap-1 text-xs text-gray-500">
                      <Clock className="w-3 h-3" />
                      {supportHours}
                    </div>
                  </div>
                </div>
              </>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </>
  )
}
