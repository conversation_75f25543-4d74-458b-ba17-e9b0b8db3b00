import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase';
import { verifySession } from '@/lib/supabase';
import type { CreateBookingData, BookingFilters } from '@/types/database';

// Generate unique booking reference
function generateBookingReference(): string {
  const prefix = 'P7';
  const timestamp = Date.now().toString().slice(-6);
  const random = Math.random().toString(36).substring(2, 6).toUpperCase();
  return `${prefix}${timestamp}${random}`;
}

// GET /api/bookings - Get bookings with filtering
export async function GET(request: NextRequest) {
  try {
    const session = await verifySession();
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const supabase = createServerSupabase();

    // Parse query parameters
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const dateFrom = searchParams.get('dateFrom');
    const dateTo = searchParams.get('dateTo');
    const userId = searchParams.get('userId');
    const tripId = searchParams.get('tripId');

    // Check if user is admin
    const { data: user } = await supabase
      .from('users')
      .select('role')
      .eq('id', session.user.id)
      .single();

    const isAdmin = user?.role === 'admin';

    // Build query
    let query = supabase
      .from('bookings')
      .select(`
        *,
        user:users(id, full_name, email, phone),
        trip:trips(id, title, destination, duration_days, price_per_person)
      `, { count: 'exact' });

    // Non-admin users can only see their own bookings
    if (!isAdmin) {
      query = query.eq('user_id', session.user.id);
    }

    // Apply filters
    if (status) {
      query = query.eq('status', status);
    }

    if (dateFrom) {
      query = query.gte('booking_date', dateFrom);
    }

    if (dateTo) {
      query = query.lte('booking_date', dateTo);
    }

    if (userId && isAdmin) {
      query = query.eq('user_id', userId);
    }

    if (tripId) {
      query = query.eq('trip_id', tripId);
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to);

    // Order by created_at descending
    query = query.order('created_at', { ascending: false });

    const { data: bookings, error, count } = await query;

    if (error) {
      console.error('Error fetching bookings:', error);
      return NextResponse.json(
        { error: 'Failed to fetch bookings' },
        { status: 500 }
      );
    }

    const totalPages = Math.ceil((count || 0) / limit);

    return NextResponse.json({
      data: bookings,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages,
      },
    });
  } catch (error) {
    console.error('Error in GET /api/bookings:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/bookings - Create a new booking
export async function POST(request: NextRequest) {
  try {
    const session = await verifySession();
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body: CreateBookingData = await request.json();
    const supabase = createServerSupabase();

    // Validate required fields
    const requiredFields = ['trip_id', 'number_of_participants', 'emergency_contact', 'participants', 'booking_date'];
    for (const field of requiredFields) {
      if (!body[field as keyof CreateBookingData]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Validate participants count
    if (body.participants.length !== body.number_of_participants) {
      return NextResponse.json(
        { error: 'Number of participants does not match participants array length' },
        { status: 400 }
      );
    }

    // Get trip details and validate availability
    const { data: trip, error: tripError } = await supabase
      .from('trips')
      .select('*')
      .eq('id', body.trip_id)
      .eq('is_active', true)
      .single();

    if (tripError || !trip) {
      return NextResponse.json(
        { error: 'Trip not found or not available' },
        { status: 404 }
      );
    }

    // Check if trip is available for the booking date
    if (trip.available_from && body.booking_date < trip.available_from) {
      return NextResponse.json(
        { error: 'Trip is not available for the selected date' },
        { status: 400 }
      );
    }

    if (trip.available_to && body.booking_date > trip.available_to) {
      return NextResponse.json(
        { error: 'Trip is not available for the selected date' },
        { status: 400 }
      );
    }

    // Check participant limits
    if (body.number_of_participants < trip.min_participants) {
      return NextResponse.json(
        { error: `Minimum ${trip.min_participants} participants required` },
        { status: 400 }
      );
    }

    if (body.number_of_participants > trip.max_participants) {
      return NextResponse.json(
        { error: `Maximum ${trip.max_participants} participants allowed` },
        { status: 400 }
      );
    }

    // Check for booking conflicts (same trip, same date)
    const { data: existingBookings } = await supabase
      .from('bookings')
      .select('number_of_participants')
      .eq('trip_id', body.trip_id)
      .eq('booking_date', body.booking_date)
      .in('status', ['pending', 'confirmed']);

    const totalBooked = existingBookings?.reduce((sum, booking) => sum + booking.number_of_participants, 0) || 0;
    const remainingSlots = trip.max_participants - totalBooked;

    if (body.number_of_participants > remainingSlots) {
      return NextResponse.json(
        { error: `Only ${remainingSlots} slots available for this date` },
        { status: 400 }
      );
    }

    // Calculate total amount
    const totalAmount = trip.price_per_person * body.number_of_participants;

    // Generate booking reference
    const bookingReference = generateBookingReference();

    // Create booking
    const { data: booking, error } = await supabase
      .from('bookings')
      .insert({
        user_id: session.user.id,
        trip_id: body.trip_id,
        booking_reference: bookingReference,
        number_of_participants: body.number_of_participants,
        total_amount: totalAmount,
        special_requirements: body.special_requirements,
        emergency_contact: body.emergency_contact,
        participants: body.participants,
        booking_date: body.booking_date,
        status: 'pending',
        payment_details: {
          method: 'pending',
          payment_status: 'pending',
          amount_paid: 0,
        },
      })
      .select(`
        *,
        user:users(id, full_name, email, phone),
        trip:trips(id, title, destination, duration_days, price_per_person)
      `)
      .single();

    if (error) {
      console.error('Error creating booking:', error);
      return NextResponse.json(
        { error: 'Failed to create booking' },
        { status: 500 }
      );
    }

    // TODO: Send confirmation email
    // TODO: Create payment intent with payment gateway

    return NextResponse.json({
      data: booking,
      message: 'Booking created successfully',
    }, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/bookings:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
