import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase';
import { verifySession } from '@/lib/supabase';

interface RouteParams {
  params: {
    id: string;
  };
}

// GET /api/trips/[id]/images - Get all images for a trip
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const { id } = params;
    const supabase = createServerSupabase();

    const { data: images, error } = await supabase
      .from('trip_images')
      .select('*')
      .eq('trip_id', id)
      .order('sort_order', { ascending: true })
      .order('created_at', { ascending: true });

    if (error) {
      console.error('Error fetching trip images:', error);
      return NextResponse.json(
        { error: 'Failed to fetch trip images' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      data: images,
    });
  } catch (error) {
    console.error('Error in GET /api/trips/[id]/images:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/trips/[id]/images - Upload images for a trip (Admin only)
export async function POST(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await verifySession('admin');
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = params;
    const formData = await request.formData();
    const files = formData.getAll('images') as File[];
    const altTexts = formData.getAll('altTexts') as string[];
    const isFeatured = formData.get('isFeatured') === 'true';

    if (!files || files.length === 0) {
      return NextResponse.json(
        { error: 'No images provided' },
        { status: 400 }
      );
    }

    const supabase = createServerSupabase();

    // Verify trip exists
    const { data: trip, error: tripError } = await supabase
      .from('trips')
      .select('id')
      .eq('id', id)
      .single();

    if (tripError || !trip) {
      return NextResponse.json(
        { error: 'Trip not found' },
        { status: 404 }
      );
    }

    const uploadedImages = [];
    const errors = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const altText = altTexts[i] || '';

      try {
        // Generate unique filename
        const fileExt = file.name.split('.').pop();
        const fileName = `trips/${id}/${Date.now()}-${Math.random().toString(36).substring(7)}.${fileExt}`;

        // Upload to Supabase Storage
        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('trip-images')
          .upload(fileName, file);

        if (uploadError) {
          errors.push(`Failed to upload ${file.name}: ${uploadError.message}`);
          continue;
        }

        // Get public URL
        const { data: { publicUrl } } = supabase.storage
          .from('trip-images')
          .getPublicUrl(fileName);

        // Get current max sort order
        const { data: maxSortData } = await supabase
          .from('trip_images')
          .select('sort_order')
          .eq('trip_id', id)
          .order('sort_order', { ascending: false })
          .limit(1)
          .single();

        const nextSortOrder = (maxSortData?.sort_order || 0) + 1;

        // Save image record to database
        const { data: imageRecord, error: dbError } = await supabase
          .from('trip_images')
          .insert({
            trip_id: id,
            image_url: publicUrl,
            alt_text: altText,
            is_featured: isFeatured && i === 0, // Only first image can be featured
            sort_order: nextSortOrder + i,
          })
          .select()
          .single();

        if (dbError) {
          errors.push(`Failed to save ${file.name} to database: ${dbError.message}`);
          // Clean up uploaded file
          await supabase.storage
            .from('trip-images')
            .remove([fileName]);
          continue;
        }

        uploadedImages.push(imageRecord);
      } catch (error) {
        errors.push(`Error processing ${file.name}: ${error}`);
      }
    }

    if (uploadedImages.length === 0) {
      return NextResponse.json(
        { error: 'Failed to upload any images', details: errors },
        { status: 500 }
      );
    }

    return NextResponse.json({
      data: uploadedImages,
      message: `Successfully uploaded ${uploadedImages.length} image(s)`,
      errors: errors.length > 0 ? errors : undefined,
    }, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/trips/[id]/images:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/trips/[id]/images - Update image order and properties (Admin only)
export async function PUT(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await verifySession('admin');
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = params;
    const { images } = await request.json();

    if (!Array.isArray(images)) {
      return NextResponse.json(
        { error: 'Images must be an array' },
        { status: 400 }
      );
    }

    const supabase = createServerSupabase();

    // Update each image
    const updatePromises = images.map((image, index) => 
      supabase
        .from('trip_images')
        .update({
          sort_order: index,
          alt_text: image.alt_text,
          is_featured: image.is_featured || false,
        })
        .eq('id', image.id)
        .eq('trip_id', id)
    );

    const results = await Promise.all(updatePromises);
    const errors = results.filter(result => result.error);

    if (errors.length > 0) {
      console.error('Error updating images:', errors);
      return NextResponse.json(
        { error: 'Failed to update some images' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: 'Images updated successfully',
    });
  } catch (error) {
    console.error('Error in PUT /api/trips/[id]/images:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
