'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Users, 
  MapPin, 
  Calendar, 
  Award,
  School,
  Star,
  Globe,
  Heart
} from 'lucide-react'

const STATS = [
  {
    icon: Users,
    value: 50000,
    suffix: '+',
    label: 'Students Impacted',
    description: 'Lives transformed through educational travel',
    color: 'from-blue-500 to-blue-600'
  },
  {
    icon: School,
    value: 500,
    suffix: '+',
    label: 'Schools Partnered',
    description: 'Educational institutions across India',
    color: 'from-green-500 to-green-600'
  },
  {
    icon: MapPin,
    value: 100,
    suffix: '+',
    label: 'Destinations',
    description: 'Carefully curated learning locations',
    color: 'from-purple-500 to-purple-600'
  },
  {
    icon: Calendar,
    value: 15,
    suffix: '+',
    label: 'Years of Excellence',
    description: 'Pioneering educational tourism since 2009',
    color: 'from-orange-500 to-orange-600'
  },
  {
    icon: Award,
    value: 25,
    suffix: '+',
    label: 'Awards & Recognition',
    description: 'Industry accolades and certifications',
    color: 'from-yellow-500 to-yellow-600'
  },
  {
    icon: Star,
    value: 4.8,
    suffix: '/5',
    label: 'Average Rating',
    description: 'Based on 1000+ reviews from students and parents',
    color: 'from-pink-500 to-pink-600'
  },
  {
    icon: Globe,
    value: 12,
    suffix: '',
    label: 'States Covered',
    description: 'Diverse destinations across India',
    color: 'from-teal-500 to-teal-600'
  },
  {
    icon: Heart,
    value: 98,
    suffix: '%',
    label: 'Satisfaction Rate',
    description: 'Students who would recommend us',
    color: 'from-red-500 to-red-600'
  }
]

// Counter animation hook
function useCounter(end: number, duration: number = 2000) {
  const [count, setCount] = useState(0)
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    if (!isVisible) return

    let startTime: number
    let animationFrame: number

    const animate = (currentTime: number) => {
      if (!startTime) startTime = currentTime
      const progress = Math.min((currentTime - startTime) / duration, 1)
      
      setCount(Math.floor(progress * end))
      
      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate)
      }
    }

    animationFrame = requestAnimationFrame(animate)
    
    return () => cancelAnimationFrame(animationFrame)
  }, [end, duration, isVisible])

  return { count, setIsVisible }
}

function StatCard({ stat, index }: { stat: typeof STATS[0]; index: number }) {
  const { count, setIsVisible } = useCounter(stat.value)

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ delay: index * 0.1 }}
      onViewportEnter={() => setIsVisible(true)}
      className="group"
    >
      <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 text-center h-full">
        <div className={`w-16 h-16 bg-gradient-to-r ${stat.color} rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300`}>
          <stat.icon className="w-8 h-8 text-white" />
        </div>
        
        <div className="mb-4">
          <div className="text-4xl font-bold text-gray-900 mb-2">
            {stat.value === 4.8 ? count.toFixed(1) : count.toLocaleString()}
            <span className={`text-transparent bg-clip-text bg-gradient-to-r ${stat.color}`}>
              {stat.suffix}
            </span>
          </div>
          <h3 className="text-xl font-semibold text-gray-900">{stat.label}</h3>
        </div>
        
        <p className="text-gray-600 text-sm leading-relaxed">{stat.description}</p>
      </div>
    </motion.div>
  )
}

export function StatsSection() {
  return (
    <section className="py-20">
      <div className="max-w-7xl mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">Our Impact in Numbers</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            These numbers represent more than statistics - they represent lives touched, minds opened, and futures shaped through the power of educational travel
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {STATS.map((stat, index) => (
            <StatCard key={index} stat={stat} index={index} />
          ))}
        </div>

        {/* Additional Context */}
        <div className="mt-16 bg-gradient-to-r from-blue-50 to-green-50 rounded-2xl p-8">
          <div className="text-center">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              More Than Just Numbers
            </h3>
            <p className="text-gray-700 max-w-4xl mx-auto leading-relaxed">
              Behind every statistic is a story of transformation. From the shy student who discovered confidence on a mountain trek, 
              to the urban child who learned about rural life in a village homestay, to the group that bonded over a campfire under 
              the stars - these experiences create memories and learning that last a lifetime.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8 mt-8">
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">Zero</div>
              <div className="text-gray-700">Major Safety Incidents</div>
              <div className="text-sm text-gray-600 mt-1">in 15+ years of operations</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">100%</div>
              <div className="text-gray-700">Curriculum Aligned</div>
              <div className="text-sm text-gray-600 mt-1">programs designed with educators</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600 mb-2">24/7</div>
              <div className="text-gray-700">Support Available</div>
              <div className="text-sm text-gray-600 mt-1">throughout every journey</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
