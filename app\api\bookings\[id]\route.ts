import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase';
import { verifySession } from '@/lib/supabase';
import type { BookingStatus } from '@/types/database';

interface RouteParams {
  params: {
    id: string;
  };
}

// GET /api/bookings/[id] - Get a specific booking
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await verifySession();
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = params;
    const supabase = createServerSupabase();

    // Check if user is admin
    const { data: user } = await supabase
      .from('users')
      .select('role')
      .eq('id', session.user.id)
      .single();

    const isAdmin = user?.role === 'admin';

    let query = supabase
      .from('bookings')
      .select(`
        *,
        user:users(id, full_name, email, phone),
        trip:trips(id, title, destination, duration_days, price_per_person, featured_image_url)
      `);

    // Check if ID is booking reference or UUID
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id);
    
    if (isUUID) {
      query = query.eq('id', id);
    } else {
      query = query.eq('booking_reference', id);
    }

    // Non-admin users can only see their own bookings
    if (!isAdmin) {
      query = query.eq('user_id', session.user.id);
    }

    const { data: booking, error } = await query.single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Booking not found' },
          { status: 404 }
        );
      }
      console.error('Error fetching booking:', error);
      return NextResponse.json(
        { error: 'Failed to fetch booking' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      data: booking,
    });
  } catch (error) {
    console.error('Error in GET /api/bookings/[id]:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/bookings/[id] - Update a booking
export async function PUT(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await verifySession();
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = params;
    const body = await request.json();
    const supabase = createServerSupabase();

    // Check if user is admin
    const { data: user } = await supabase
      .from('users')
      .select('role')
      .eq('id', session.user.id)
      .single();

    const isAdmin = user?.role === 'admin';

    // Get existing booking
    let query = supabase
      .from('bookings')
      .select('*');

    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id);
    
    if (isUUID) {
      query = query.eq('id', id);
    } else {
      query = query.eq('booking_reference', id);
    }

    if (!isAdmin) {
      query = query.eq('user_id', session.user.id);
    }

    const { data: existingBooking, error: fetchError } = await query.single();

    if (fetchError || !existingBooking) {
      return NextResponse.json(
        { error: 'Booking not found' },
        { status: 404 }
      );
    }

    // Determine what can be updated based on user role and booking status
    let allowedUpdates: string[] = [];

    if (isAdmin) {
      // Admins can update everything
      allowedUpdates = ['status', 'payment_details', 'admin_notes', 'special_requirements'];
    } else {
      // Regular users can only update certain fields for pending bookings
      if (existingBooking.status === 'pending') {
        allowedUpdates = ['special_requirements', 'participants', 'emergency_contact'];
      } else {
        return NextResponse.json(
          { error: 'Cannot modify confirmed or completed bookings' },
          { status: 400 }
        );
      }
    }

    // Filter updates to only allowed fields
    const filteredUpdates: any = {};
    for (const key of allowedUpdates) {
      if (body[key] !== undefined) {
        filteredUpdates[key] = body[key];
      }
    }

    // Validate status change if admin is updating status
    if (isAdmin && body.status) {
      const validStatuses: BookingStatus[] = ['pending', 'confirmed', 'cancelled', 'completed'];
      if (!validStatuses.includes(body.status)) {
        return NextResponse.json(
          { error: 'Invalid booking status' },
          { status: 400 }
        );
      }

      // Validate status transitions
      const currentStatus = existingBooking.status;
      const newStatus = body.status;

      // Define valid status transitions
      const validTransitions: Record<BookingStatus, BookingStatus[]> = {
        pending: ['confirmed', 'cancelled'],
        confirmed: ['completed', 'cancelled'],
        cancelled: [], // Cannot change from cancelled
        completed: [], // Cannot change from completed
      };

      if (!validTransitions[currentStatus].includes(newStatus)) {
        return NextResponse.json(
          { error: `Cannot change status from ${currentStatus} to ${newStatus}` },
          { status: 400 }
        );
      }
    }

    if (Object.keys(filteredUpdates).length === 0) {
      return NextResponse.json(
        { error: 'No valid updates provided' },
        { status: 400 }
      );
    }

    // Update booking
    const { data: booking, error } = await supabase
      .from('bookings')
      .update({
        ...filteredUpdates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', existingBooking.id)
      .select(`
        *,
        user:users(id, full_name, email, phone),
        trip:trips(id, title, destination, duration_days, price_per_person)
      `)
      .single();

    if (error) {
      console.error('Error updating booking:', error);
      return NextResponse.json(
        { error: 'Failed to update booking' },
        { status: 500 }
      );
    }

    // TODO: Send notification emails for status changes
    // TODO: Handle payment processing for status changes

    return NextResponse.json({
      data: booking,
      message: 'Booking updated successfully',
    });
  } catch (error) {
    console.error('Error in PUT /api/bookings/[id]:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/bookings/[id] - Cancel a booking
export async function DELETE(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await verifySession();
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = params;
    const supabase = createServerSupabase();

    // Check if user is admin
    const { data: user } = await supabase
      .from('users')
      .select('role')
      .eq('id', session.user.id)
      .single();

    const isAdmin = user?.role === 'admin';

    // Get existing booking
    let query = supabase
      .from('bookings')
      .select('*');

    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id);
    
    if (isUUID) {
      query = query.eq('id', id);
    } else {
      query = query.eq('booking_reference', id);
    }

    if (!isAdmin) {
      query = query.eq('user_id', session.user.id);
    }

    const { data: existingBooking, error: fetchError } = await query.single();

    if (fetchError || !existingBooking) {
      return NextResponse.json(
        { error: 'Booking not found' },
        { status: 404 }
      );
    }

    // Check if booking can be cancelled
    if (existingBooking.status === 'completed') {
      return NextResponse.json(
        { error: 'Cannot cancel completed booking' },
        { status: 400 }
      );
    }

    if (existingBooking.status === 'cancelled') {
      return NextResponse.json(
        { error: 'Booking is already cancelled' },
        { status: 400 }
      );
    }

    // Update booking status to cancelled
    const { data: booking, error } = await supabase
      .from('bookings')
      .update({
        status: 'cancelled',
        updated_at: new Date().toISOString(),
      })
      .eq('id', existingBooking.id)
      .select()
      .single();

    if (error) {
      console.error('Error cancelling booking:', error);
      return NextResponse.json(
        { error: 'Failed to cancel booking' },
        { status: 500 }
      );
    }

    // TODO: Process refund if applicable
    // TODO: Send cancellation confirmation email

    return NextResponse.json({
      data: booking,
      message: 'Booking cancelled successfully',
    });
  } catch (error) {
    console.error('Error in DELETE /api/bookings/[id]:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
